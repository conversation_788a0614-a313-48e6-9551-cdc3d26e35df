<!-- 以下文件中 声明的 firstIndicatorId 字段 如果是 0  则说明这个是处于输入框激活的状态  如果不是0则数据保存下来了 -->
<!-- 以下文件中 声明的 secondIndicatorId 字段 如果是 -1  则说明这个是处于输入框激活的状态  如果不是-1则这条数据保存下来了 -->
<!-- 到时候处理数据的直接遍历后端的id 字段 去替换 firstIndicatorId  secondIndicatorId -->
<!--  0学生 1老师 2家长 -->

<template>
    <!-- 总数据: {{ data.rulesData }} -->
    <div class="step_two_box" v-for="(item, index) in data.rulesData" :key="index">
        <a-form-item
            label="评价规则名称"
            :labelCol="{ span: 24 }"
            mb-10
            :name="['rulesData', index, 'name']"
            :rules="[{ required: true, message: '请输入评价规则名称', trigger: 'blur' }]"
        >
            <a-input
                v-model:value="item.name"
                :disabled="data.status == 2"
                placeholder="请输入"
                show-count
                :maxlength="30"
            />
        </a-form-item>
        <a-form-item
            label="评价规则"
            labelAlign="left"
            :labelCol="{ span: 4 }"
            mb-10
            :name="['rulesData', index, 'ruleType']"
            :rules="[{ required: true, message: '请选择评价规则', trigger: ['change', 'blur'] }]"
        >
            <a-radio-group v-model:value="item.ruleType" w-full flex items-center :disabled="data.status == 1 || data.status == 2">
                <a-radio :value="0">新建规则</a-radio>
                <a-radio :value="1">选择模板</a-radio>
                <!-- 这个a-input 是触发选择模板的分页弹窗的数据 回显勾选的名字 -->
                <a-input
                    v-show="item.ruleType === 1"
                    v-model:value="item.publicRuleName"
                    @click="openRuleModal(item, index)"
                    readOnly
                    :disabled="data.status == 1 || data.status == 2"
                    placeholder="请选择规则模板"
                />
            </a-radio-group>
        </a-form-item>
        <a-form-item
            label="参与人员"
            :labelCol="{ span: 4 }"
            mb-10
            labelAlign="left"
            :name="['rulesData', index, 'personType']"
            :rules="[{ required: true, message: '请选择参与人员', trigger: ['change', 'blur'] }]"
        >
            <a-radio-group
                v-model:value="item.personType"
                @change="e => personTypeChange(e, index)"
                w-full
                flex
                items-center
                :disabled="data.status == 1 || data.status == 2"
            >
                <a-radio :value="0">学生</a-radio>
                <a-radio :value="1">老师</a-radio>
                <!-- 这个a-input 是触发选人组件的操作 只需要 显示名字 和打开弹窗别的都不管 -->
                <a-input
                    v-model:value="item.attendPeople"
                    readOnly
                    @click="selectpersonTypeList(item, index)"
                    :disabled="data.status == 1 || data.status == 2"
                    placeholder="请选择参与人员"
                />
            </a-radio-group>
        </a-form-item>
        <a-form-item
            label="评价规则"
            :labelCol="{ span: 24 }"
            mb-10
            labelAlign="left"
            :name="['rulesData', index, 'dataSource']"
            :rules="[{ required: true, message: '请填写评价规则', trigger: 'blur' }]"
        >
            <a-table :dataSource="item.dataSource" bordered :pagination="false" :scroll="{ x: 1300 }">
                <!-- 把table的表头放在这里处理下 -->
                <a-table-column
                    title="一级指标"
                    data-index="firstIndicatorName"
                    key="firstIndicatorName"
                    width="250px"
                    :customCell="
                        (record, rowIndex, column) => {
                            let index = 1
                            for (let i = rowIndex + 1; i < item.dataSource.length; i++) {
                                const element = item.dataSource[i]
                                if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                                    index = index + 1
                                }
                            }
                            if (rowIndex > 0) {
                                record['firstIndicatorId'] == item.dataSource[rowIndex - 1]?.firstIndicatorId && (index = 0)
                            }
                            return { rowSpan: index }
                        }
                    "
                />
                <a-table-column title="二级指标" data-index="secondIndicatorName" key="secondIndicatorName" width="250px" />
                <a-table-column title="评分标准" data-index="standardName" key="standardName" width="250px" />
                <a-table-column title="选择积分卡" data-index="scoreCardId" key="scoreCardId" width="250px"></a-table-column>
                <a-table-column title="评分范围" data-index="score" key="score" width="250px" />
                <a-table-column title="评分方式" data-index="evalScoreTypeList" key="evalScoreTypeList" width="250px" />
                <a-table-column title="评价者" data-index="evaluator" key="evaluator" width="200px" />
                <a-table-column title="操作" data-index="operate" key="operate" fixed="right" width="150px" />

                <template #headerCell="{ column }">
                    <template v-if="column.key === 'firstIndicatorName'">
                        <div class="ScoringCriteria_title" v-if="!(data.status == 1 || data.status == 2)">
                            <span>{{ column.title }}</span>
                            <img @click="addOne(item)" src="@/assets/images/evaluation/ScoringCriteriaAdd.png" class="ScoringCriteriaAdd" />
                        </div>
                    </template>
                    <template v-if="column.key === 'scoreCardId'">
                        <div>
                            <a-tooltip>
                                <template #title>如没有可选积分卡，可保存当前评价后，前往积分卡管理进行添加后选择</template>
                                选择积分卡
                                <QuestionCircleOutlined />
                            </a-tooltip>
                        </div>
                    </template>
                    <template v-if="column.key === 'evalScoreTypeList'">
                        <div class="flex items-center" v-if="!(data.status == 2)">
                            <span mr-5>评分方式</span>
                            <a-select
                                v-if="item.dataSource.length"
                                mode="multiple"
                                :options="evalScoreTypeOptions"
                                class="flex-1"
                                v-model:value="item.evalScoreTypeList"
                                @change="(value, option) => batchSelectevalScoreType(value, option, item)"
                                placeholder="请选择(多选)"
                            ></a-select>
                        </div>
                    </template>
                    <template v-if="column.key === 'evaluator'">
                        <div class="flex items-center" v-if="!(data.status == 1 || data.status == 2)">
                            <span mr-5>评价者</span>
                            <!-- 这个a-input 是触发选人组件的操作 只需要 显示名字 和打开弹窗别的都不管 -->
                            <!-- 如果table数据列表有数据才可以操作批量吧 -->
                            <a-input
                                v-if="item.dataSource.length"
                                class="flex-1"
                                readOnly
                                v-model:value="item.evaluator"
                                @click="batchSelectPeople(item)"
                                placeholder="请选择(多选)"
                            />
                        </div>
                    </template>
                </template>
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.key === 'firstIndicatorName'">
                        <div v-if="record.firstIndicatorEdit" class="ScoringCriteria_title">
                            <a-textarea autosize v-model:value="record.firstIndicatorName" :maxlength="50" placeholder="请输入" />
                            <div class="iconcenter">
                                <img
                                    @click="closeOne(record, index, item)"
                                    src="@/assets/images/evaluation/x.png"
                                    class="ScoringCriteriaAdd"
                                />
                                <img
                                    @click="addIndicator(record, item)"
                                    src="@/assets/images/evaluation/dui.png"
                                    class="ScoringCriteriaAdd"
                                />
                            </div>
                        </div>
                        <div class="ScoringCriteria_title" v-else>
                            <span>{{ record.firstIndicatorName }}</span>
                            <div class="iconcenter">
                                <img
                                    @click="editFir(record)"
                                    v-if="!(data.status === 2)"
                                    src="@/assets/images/evaluation/ScoringCriteriaedit.png"
                                    class="ScoringCriteriaAdd editFir"
                                />
                                <!-- 当firstIndicatorId为0的时候 说明数据根本就没有保存下来不给加二级指标 -->
                                <!-- 发现数组中有正在编辑的二级指标就不让他再加二级指标了 -->
                                <img
                                    @click="addTwo(record, item)"
                                    v-if="record.firstIndicatorId && !isEditing(item) && !data.status"
                                    src="@/assets/images/evaluation/ScoringCriteriaAdd.png"
                                    class="ScoringCriteriaAdd"
                                />
                            </div>
                        </div>
                    </template>
                    <template v-if="column.key === 'secondIndicatorName'">
                        <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title">
                            <a-textarea
                                autosize
                                :disabled="data.status == 2"
                                v-model:value="record.secondIndicatorName"
                                :maxlength="200"
                                placeholder="请输入"
                            />
                            <div class="iconcenter"></div>
                        </div>
                        <div class="ScoringCriteria_title" v-else>
                            <span>{{ record.secondIndicatorName }}</span>
                        </div>
                    </template>
                    <template v-if="column.key === 'standardName'">
                        <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title">
                            <a-textarea
                                autosize
                                :disabled="data.status == 2"
                                v-model:value="record.standardName"
                                :maxlength="1000"
                                placeholder="请输入"
                            />
                        </div>
                        <div class="ScoringCriteria_title" v-else>
                            <span>{{ record.standardName }}</span>
                        </div>
                    </template>

                    <template v-if="column.key === 'scoreCardId'">
                        <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title">
                            <a-select
                                :disabled="data.status == 1 || data.status == 2"
                                v-model:value="record.scoreCardId"
                                :options="jifenOptions"
                                placeholder="请选择积分卡"
                                @change="(value, option) => changeJifen(value, option, record)"
                                :fieldNames="{ label: 'scoreCardName', value: 'id' }"
                            ></a-select>
                        </div>
                        <div class="ScoringCriteria_title" v-else>
                            <span>{{ record.scoreCardName }}</span>
                        </div>
                    </template>
                    <template v-if="column.key === 'score'">
                        <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title items-center">
                            <a-input-number
                                placeholder="≥-100"
                                v-model:value="record.startScore"
                                :max="100"
                                :min="-100"
                                :precision="0"
                                :disabled="data.status == 1 || data.status == 2"
                            />
                            <span pr-5 pl-5>-</span>
                            <a-input-number
                                placeholder="≤100"
                                v-model:value="record.endScore"
                                :max="100"
                                :min="-100"
                                :precision="0"
                                :disabled="data.status == 1 || data.status == 2"
                            />
                        </div>
                        <div class="flex justify-center" v-else>
                            <span>{{ record.startScore }}</span>
                            <span>-</span>
                            <span>{{ record.endScore }}</span>
                        </div>
                    </template>

                    <!-- 评分方式 -->
                    <template v-if="column.key === 'evalScoreTypeList'">
                        <div v-if="record.secondIndicatorEdit">
                            <a-select
                                mode="multiple"
                                v-model:value="record.evalScoreTypeList"
                                :options="evalScoreTypeOptions"
                                placeholder="请选择"
                                :disabled="data.status == 2"
                            ></a-select>
                        </div>
                        <div v-else>
                            {{ arrayToDescription(record.evalScoreTypeList || []) }}
                        </div>
                    </template>
                    <template v-if="column.key === 'evaluator'">
                        <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title">
                            <!-- 这个a-input 是触发选人组件的操作 只需要 显示名字 和打开弹窗别的都不管 -->
                            <a-input
                                :disabled="data.status == 1 || data.status == 2"
                                v-model:value="record.showLineName"
                                readOnly
                                placeholder="请选择"
                                @click="lineSelectPeople(record)"
                            />
                        </div>
                        <div class="ScoringCriteria_title" v-else>
                            <span>{{ record.showLineName }}</span>
                        </div>
                    </template>
                    <template v-else-if="column.key === 'operate'">
                        <div v-if="record.secondIndicatorEdit">
                            <a-button type="link" style="color: #00b781" @click="addIndicators(record)">保存</a-button>
                            <a-button type="link" danger @click="delTwo(record, index, item)" style="padding: 0; margin: 0">取消</a-button>
                        </div>
                        <div v-else>
                            <a-button type="link" style="color: #00b781" @click="editTwo(record)" v-if="record.firstIndicatorId != 0">
                                编辑
                            </a-button>
                            <a-button
                                v-if="record.firstIndicatorId != 0 && data.status != 1 && data.status != 2"
                                type="link"
                                danger
                                @click="delOperate(record, index, item)"
                                style="padding: 0; margin: 0"
                            >
                                删除
                            </a-button>
                        </div>
                    </template>
                </template>
            </a-table>
        </a-form-item>
        <div class="delIcon" v-if="index && !(data.status === 1)" @click="delItem(index)">
            <MinusCircleOutlined />
            删除
        </div>
    </div>
    <a-button type="primary" ghost mt-20 @click="addRule" :disabled="data.status == 1 || data.status == 2">
        <PlusOutlined />
        添加规则
    </a-button>

    <ModelSelect v-model:openVisible="modelState.openVisible" :tabs="state.peopleTabs" :selected="state.selectEcho" />

    <!-- 写一个选择规则的弹窗 -->
    <a-modal
        v-model:open="state.openRuleBox"
        :maskClosable="false"
        :keyboard="false"
        title="评价规则库"
        :bodyStyle="{ overflow: 'auto', height: '550px' }"
        width="1000px"
        @ok="handleOkRule"
    >
        <div p-20>
            <div class="searchBox">
                <a-form layout="inline">
                    <a-form-item label="评价规则">
                        <a-input style="width: 300px" v-model:value.trim="state.ruleSearch.name" placeholder="请输入规则名称" />
                    </a-form-item>
                    <a-form-item label="使用次数范围">
                        <a-input
                            style="width: 100px"
                            v-model:value="state.ruleSearch.minCount"
                            @keyup="state.ruleSearch.minCount = state.ruleSearch.minCount.replace(/\D/g, '')"
                            placeholder="请输入"
                        />
                        -
                        <a-input
                            style="width: 100px"
                            v-model:value="state.ruleSearch.maxCount"
                            @keyup="state.ruleSearch.maxCount = state.ruleSearch.maxCount.replace(/\D/g, '')"
                            placeholder="请输入"
                        />
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="inquireRule">查询</a-button>
                        <a-button @click="resetRule">重置</a-button>
                    </a-form-item>
                </a-form>
            </div>
            <div class="tableBox">
                <a-table
                    :columns="ruleColumns"
                    rowKey="id"
                    :row-selection="{ type: 'radio', selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
                    :data-source="state.ruleData"
                    @change="handleChange"
                    :pagination="false"
                >
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'operate'">
                            <a class="btn-link-color" @click="handleDetail(record)">详情</a>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
    </a-modal>

    <a-modal
        v-model:open="state.ruleModel"
        title="评价规则详情"
        :footer="null"
        :width="1000"
        :destroyOnClose="true"
        :keyboard="false"
        :maskClosable="false"
        :bodyStyle="{ overflow: 'auto', height: '620px' }"
    >
        <div p-20>
            <a-table :dataSource="state.ruledataSource" bordered :pagination="false">
                <a-table-column
                    title="一级指标"
                    data-index="firstIndicatorName"
                    key="firstIndicatorName"
                    :customCell="
                        (record, rowIndex, column) => {
                            let index = 1
                            for (let i = rowIndex + 1; i < state.ruledataSource.length; i++) {
                                const element = state.ruledataSource[i]
                                if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                                    index = index + 1
                                }
                            }
                            if (rowIndex > 0) {
                                record['firstIndicatorId'] == state.ruledataSource[rowIndex - 1]?.firstIndicatorId && (index = 0)
                            }
                            return { rowSpan: index }
                        }
                    "
                />
                <a-table-column title="二级指标" data-index="secondIndicatorName" key="secondIndicatorName" />
                <a-table-column title="评分标准" data-index="content" key="content" />
                <a-table-column title="评分范围" data-index="range" key="range" :width="100" />
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'range'">
                        <div class="flex justify-center">
                            <span>{{ record.minScore }}</span>
                            <span>-</span>
                            <span>{{ record.maxScore }}</span>
                        </div>
                    </template>
                </template>
            </a-table>
        </div>
    </a-modal>
</template>
<script setup>
// import { ZhihuCircleFilled } from '@ant-design/icons-vue'
// import { SELECT_TYPE, DISPLAY_MODE } from '@/components/common/ModelSelectNew/constants.js'
// const modelSelectRef = ref(null)

import { ruleColumns } from '../configData.js'

// 生成一个随机id , 生成出同样的那就当他是倒霉蛋算了
function generateRandomId(length) {
    // 初始化一个数组，包含0到9的数字
    const digits = Array.from({ length: 10 }, (_, i) => i)
    // 生成指定长度的随机数字串
    return Array.from({ length }, () => digits[Math.floor(Math.random() * digits.length)]).join('')
}

const evalScoreTypeOptions = ref([
    { value: 'image', label: '图片' },
    { value: 'video', label: '视频' },
    { value: 'text', label: '文字' },
    { value: 'score', label: '分数' },
])

function arrayToDescription(arr) {
    const mapping = {
        image: '图片',
        video: '视频',
        text: '文字',
        score: '分数',
    }

    // 通过map替换数组中的每个元素为对应描述
    const descriptions = arr.map(item => mapping[item] || item)
    // 将描述数组合成字符串，用空格分隔
    return descriptions.join(',')
}

// 获取这个学校是k12 还是大学的学校  1是k12  2是大学
// 1选择老师家长  2选择学生老师
const mainStore = useStore()
const schoolType = mainStore.commentJsonKeys?.schoolType
let allSelect = [
    {
        tab: '教职工',
        checked: true,
        checkVisible: 'all',
        personField: { key: 'typeValue', value: ['people_dept'] },
        id: 1,
        single: false,
        searchOption: {
            show: true,
            displayMode: 'old',
        },
    },
    {
        tab: '家长',
        checked: true,
        checkVisible: 'all',
        id: 2,
        personField: { key: 'typeValue', value: ['student'] },
        single: false,
        searchOption: {
            show: true,
            displayMode: 'old',
        },
    },
]
if (schoolType === 1) {
    allSelect = [
        {
            tab: '教职工',
            checked: true,
            checkVisible: 'all',
            personField: { key: 'typeValue', value: ['people_dept'] },
            id: 1,
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
        {
            tab: '家长',
            checked: true,
            checkVisible: 'all',
            id: 2,
            personField: { key: 'typeValue', value: ['student'] },
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ]
}
if (schoolType === 2) {
    allSelect = [
        {
            tab: '教职工',
            checked: true,
            checkVisible: 'all',
            personField: { key: 'typeValue', value: ['people_dept'] },
            id: 1,
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
        {
            tab: '学生',
            checked: true,
            checkVisible: 'all',
            id: 0,
            personField: { key: 'typeValue', value: ['student'] },
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ]
}

const props = defineProps({
    data: {
        type: Object,
        default: {},
    },
    validateInfos: {
        type: Object,
        default: {},
    },
    jifenOptions: {
        type: Array,
        default: [],
    },
})

const state = reactive({
    // stepTwo: [{ name: '', ruleName: '', personType: 0, evaluator: '', dataSource: [] }],
    selectiveType: '', //记录一下选人点击确认到底是给谁
    peopleTabs: [
        {
            tab: '教职工',
            checked: true,
            checkVisible: 'all',
            id: 1,
            personField: { key: 'typeValue', value: ['people_dept'] },
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ],
    // 单行的临时对象
    temporaryObj: {},
    // 整个item的临时对象
    batchtemporaryObj: {},
    selectEcho: [],
    openRuleBox: false,
    selectedRowKeys: [],
    ruleData: [],
    ruleSearch: {},
    ruleModel: false,
    ruledataSource: [],
})

// 点击查看评价规则详情
const handleDetail = data => {
    http.get('/cloud/evalPublicRule/get', { id: data.id }).then(res => {
        const tableData = res.data.firstIndicators.flatMap(item => {
            let data = []
            item.secondIndicators.forEach(secondIndicator => {
                data.push({
                    firstIndicatorId: item.id,
                    firstIndicatorName: item.name,
                    secondIndicatorId: secondIndicator.id,
                    secondIndicatorName: secondIndicator.name,
                    content: secondIndicator.indicatorScore.content,
                    minScore: secondIndicator.indicatorScore.minScore,
                    maxScore: secondIndicator.indicatorScore.maxScore,
                })
            })
            return data
        })
        state.ruledataSource = tableData
        state.ruleModel = true
    })
}

const firName = reactive({})
const secondIndicatorList = reactive({})

// 这一步只需要清除他自己本来的数据
// 则一块只有两个逻辑 firstIndicatorId 是 0  他点取消了 就直接给他删了
// 不是0 就肯定是修改的数据取消 那就别动数据啊之前是啥 给他恢复一下啊
const closeOne = (row, index, item) => {
    if (row.firstIndicatorId === 0) {
        item.dataSource.splice(index, 1)
    } else {
        row.firstIndicatorName = firName[row.firstIndicatorId]
        row.firstIndicatorEdit = false
    }
}

const addIndicator = (item, origin) => {
    if (!item.firstIndicatorName) {
        YMessage.warning('一级指标名称不可为空!')
        return false
    }
    // 点击对号 有两种情况 要么是从刚创建的到填写, 要么是点之前的数据修改, 刚创建的就把id变成随机数呗 ,修改的话就不要动之前的数据了
    // 修改并且还得去找同样的id 要不然就改乱了
    if (item.firstIndicatorId === 0) {
        item.firstIndicatorId = generateRandomId(8)
    } else {
        // 找到firstIndicatorId 同样的数据一起改了去
        origin.dataSource.forEach(
            obj => obj.firstIndicatorId === item.firstIndicatorId && (obj.firstIndicatorName = item.firstIndicatorName),
        )
    }

    // 改完那就关闭编辑状态了
    item.firstIndicatorEdit = false
}

// 保存按钮
const addIndicators = item => {
    if (!item.secondIndicatorName) {
        YMessage.warning('二级指标名称不可为空!')
        return false
    }
    // 这里还是有一样的 发现secondIndicatorId是 -1的话 就给他生成随机岁保存下来
    if (item.secondIndicatorId === -1) {
        item.secondIndicatorId = generateRandomId(8)
    }
    // 如果原来就有数据只是修改了的话,那就直接关闭就行了 反正到二级了已经是一条条数据了改就行了
    item.secondIndicatorEdit = false
}

const editTwo = item => {
    if (item.secondIndicatorId > 1) {
        // copy数据
        secondIndicatorList[item.secondIndicatorId] = JSON.parse(JSON.stringify(item))
    }
    item.secondIndicatorEdit = true
}

// 这里点击取消等于是要恢复原来的数据啊啊啊
const delTwo = (row, index, item) => {
    if (row.secondIndicatorId !== -1) {
        // 如果是secondIndicatorId 已经是随机数了 等于是修改 然后点取消这里得恢复数据
        // 恢复数据 想恢复什么就恢复什么
        row.secondIndicatorName = secondIndicatorList[row.secondIndicatorId].secondIndicatorName
        row.standardName = secondIndicatorList[row.secondIndicatorId].standardName
        row.startScore = secondIndicatorList[row.secondIndicatorId].startScore
        row.endScore = secondIndicatorList[row.secondIndicatorId].endScore
        row.showLineName = secondIndicatorList[row.secondIndicatorId].showLineName || ''
        row.valuers = secondIndicatorList[row.secondIndicatorId].valuers || []
    } else {
        // 去找一级指标 如果一级指标对应到多个二级指标的时候就直接删他
        // 如果是 一级指标对应到只有一个二级指标 那就只能做清空的操作 要不然把整个数据都删了
        let list = item.dataSource.filter(i => i.firstIndicatorId == row.firstIndicatorId)

        // 清数据
        row.secondIndicatorName = ''
        row.standardName = ''
        row.startScore = ''
        row.endScore = ''
        row.showLineName = ''
        row.valuers = []
        if (list.length > 1) {
            item.dataSource.splice(index, 1)
        }
    }
    row.secondIndicatorEdit = false
}

// 这个是从整个table数据源做删除的逻辑
const delOperate = async (row, index, item) => {
    const flag = await yConfirm('删除提示', '确定删除此数据?')
    // 找到下标直接删就完事了啊
    if (flag) {
        item.dataSource.splice(index, 1)
    }
}

const editFir = item => {
    if (item.firstIndicatorId != 0) {
        firName[item.firstIndicatorId] = item.firstIndicatorName
    }
    item.firstIndicatorEdit = true
}

// 点击新增二级指标的加号
// 需要隐藏一下 二级指标加号的按
const addTwo = (item, origin) => {
    // 这条数据中的 secondIndicatorId 处于 -1 的编辑状态的话 就不给他加一条数据
    // 直接把状态打开让他写
    if (item.secondIndicatorId === -1) {
        item.secondIndicatorEdit = true
        return false
    }

    // 在数据源origin的列表中去找firstIndicatorId 找到一样的就直接在数组中插入一条数据啊
    const index = origin.dataSource.findLastIndex(i => i.firstIndicatorId == item.firstIndicatorId)
    origin.dataSource.splice(index + 1, 0, {
        firstIndicatorId: item.firstIndicatorId,
        firstIndicatorName: item.firstIndicatorName,
        secondIndicatorId: -1,
        secondIndicatorEdit: true,
        secondIndicatorName: '',
        standardName: '',
        startScore: '',
        endScore: '',
        showLineName: '',
    })
}

// 添加一级指标如果有一级指标的输入框存在激活状态的就不让他点
const addOne = item => {
    const flag = item.dataSource.findIndex(i => i.firstIndicatorId == 0)

    if (flag == -1) {
        // 如果是新添加进来的指标就补齐所有字段啊啊啊啊
        item.dataSource.push({
            firstIndicatorId: 0,
            firstIndicatorName: '',
            secondIndicatorId: -1,
            secondIndicatorName: '',
            firstIndicatorEdit: true,
            standardName: '',
            startScore: '',
            endScore: '',
            showLineName: '',
        })
    }
}

// 添加规则 因为是多个规则啊
// 这里就是将整个的对象都加一遍啊
const addRule = () => {
    props.data.rulesData.push({ name: '', ruleName: '', ruleType: 0, personType: 0, evaluator: '', dataSource: [] })
}

// 如果过滤整个数组 发现数组中有正在编辑的二级指标就不让他再加二级指标了
const isEditing = computed(() => {
    return item => {
        const arr = item.dataSource.filter(i => i.secondIndicatorId == -1 && i.secondIndicatorEdit)
        return arr.length > 0
    }
})

// 关联弹窗
const modelState = reactive({
    openVisible: false, // 显示弹框
    dataSource: [], // 左侧数据源
    checkVisible: 'all', // 能选什么就选什么啊
    spinning: false, // loading
    disableSelect: [], // 禁止选择的ids
    searchTable: [], // 选人搜索 table 中显示
    globalID: '', // 最顶成id
})
provide('modelState', () => modelState)
provide('callbackFunction', () => ({
    search: searchSelect,
    toggleTabs,
    toggleLevel,
    cancel: closeSelect,
    submit,
}))

// 选择参与人员 打开弹窗啊啊啊啊啊啊
const selectpersonTypeList = (item, index) => {
    state.objIndex = index
    // 先简单写写吧 效果是一样的
    switch (item.personType) {
        case 0:
            // 学生
            state.peopleTabs = [
                {
                    tab: '学生',
                    checked: true,
                    checkVisible: 'all',
                    id: 0,
                    personField: { key: 'typeValue', value: ['student'] },
                    single: false,
                    searchOption: {
                        show: true,
                        displayMode: 'old',
                    },
                },
            ]
            getSelectTree({
                treeType: 1,
                businessType: 11,
            })
            break
        case 1:
            state.peopleTabs = [
                {
                    tab: '教职工',
                    checked: true,
                    checkVisible: 'all',
                    personField: { key: 'typeValue', value: ['people_dept'] },
                    id: 1,
                    single: false,
                    searchOption: {
                        show: true,
                        displayMode: 'old',
                    },
                },
            ]
            getSelectTree({
                treeType: 2,
                businessType: 21,
            })
            break
        default:
            break
    }
    state.selectiveType = 'join' // 标注一个类型
    state.selectEcho = item.participants || [] // 如果是编辑那就是有数据的participants 有就回显啊

    modelState.openVisible = true //打开
}

const batchSelectevalScoreType = (value, option, item) => {
    item.dataSource.forEach(i => {
        i.evalScoreTypeList = value
    })

    setTimeout(() => {
        item.evalScoreTypeList = []
    }, 4000)
}

// 我这块是批量选择人员  老师学生家长通通放出来 一顿乱选
const batchSelectPeople = item => {
    state.selectEcho = []
    state.batchtemporaryObj = item // 数据赋值
    state.selectiveType = 'all' // 标注一个类型
    modelState.openVisible = true
    state.peopleTabs = allSelect
    getSelectTree({ treeType: 2, businessType: 21 }) // 默认拿老师的树数据呗
}

// 我是table行数据单个 选中的数据 老师学生家长通通放出来 一顿乱选
const lineSelectPeople = data => {
    state.temporaryObj = data // 行数据赋值
    state.selectiveType = 'line' // 标注一个类型
    state.peopleTabs = allSelect
    state.selectEcho = data.valuers || []
    getSelectTree({ treeType: 2, businessType: 21 }) // 默认拿老师的树数据呗
    modelState.openVisible = true
}

// 获取到选人组件数据
function getSelectTree(options = {}) {
    /**
     * 1. 教职工 treeType：2 ， businessType：21
     * 2. 学生  treeType：1 ， businessType：11
     * 3. 家长  treeType：1 ， businessType：12
     * 4. pid第一次为0.之后根据层级id请求
     */
    modelState.spinning = true
    const params = {
        treeType: 1,
        pid: 0,
        typeValue: null,
        businessType: 11,
        code: null,
        isRule: true,
        ...options,
    }

    http.post('/cloud/v3/tree/selectTree', params)
        .then(({ data }) => {
            setDataSource(data)
        })
        .finally(() => {
            modelState.spinning = false
        })
}

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || []
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const firstLevel = !options.index
    let params = {
        treeType: item.treeType,
        businessType: item.businessType,
        typeValue: item.typeValue,
        pid: item.id,
    }
    // 首层数据
    if (firstLevel) {
        switch (tabId) {
            case 0:
                params = {
                    treeType: 1,
                    businessType: 11,
                }
                break
            case 1:
                params = {
                    treeType: 2,
                    businessType: 21,
                }
                break
            case 2:
                params = {
                    treeType: 1,
                    businessType: 12,
                }
                break
            default:
                break
        }
    }
    getSelectTree(params)
}

// 查找
function searchSelect(tabId, name) {
    // console.log('查找教职工tabId', tabId)
    modelState.spinning = true
    const options = {
        treeType: 1,
        businessType: 11,
        code: null,
        isRule: true,
        searchKey: name,
        pageNo: 1,
        // 目前没有分页，最大条数
        pageSize: 100,
    }
    switch (tabId) {
        case 0:
            options.treeType = 1
            options.businessType = 11
            break
        case 1:
            options.treeType = 2
            options.businessType = 21
            break
        case 2:
            options.treeType = 1
            options.businessType = 12
            break
        default:
            break
    }
    http.post('/cloud/v3/tree/selectTree/search', options)
        .then(({ data }) => {
            setDataSource(data.list || [])
        })
        .finally(() => {
            modelState.spinning = false
        })
}

// 切换tabs 切换不同的树结构数据 是根据tabId来切换的
function toggleTabs(tab) {
    switch (tab.id) {
        case 0:
            const options = {
                treeType: 1,
                businessType: 11,
            }
            getSelectTree(options)
            break
        case 1:
            const options1 = {
                treeType: 2,
                businessType: 21,
            }
            getSelectTree(options1)
            break
        case 2:
            const options2 = {
                treeType: 1,
                businessType: 12,
            }
            getSelectTree(options2)
            break
        default:
            break
    }
}

const closeSelect = () => {}

// 点击确定的人员 最重要的是要知道这块是确认给谁的数据啊
const submit = checked => {
    // console.log('确定好的人员checked', checked)
    switch (state.selectiveType) {
        case 'join':
            // 这里记录到下标那就直接改数据吧
            // checked 处理数据加到参与人员里面去
            props.data.rulesData[state.objIndex].participants = checked.map(item => {
                return {
                    name: item.name,
                    id: item.id,
                    identity: item._type,
                    _type: item._type,
                    treeType: item.treeType,
                    typeValue: item.typeValue,
                    businessType: item.businessType,
                }
            })
            // 回显一个input 框 纯文字
            props.data.rulesData[state.objIndex].attendPeople = checked.map(item => item.name).join('、')
            break
        case 'all':
            console.log('[ state.batchtemporaryObj ] >', state.batchtemporaryObj)
            YMessage.success('已批量添加人员')
            state.batchtemporaryObj.dataSource.forEach(item => {
                item.valuers = checked.map(item => {
                    return {
                        name: item.name,
                        id: item.id,
                        identity: item._type,
                        _type: item._type,
                        treeType: item.treeType,
                        typeValue: item.typeValue,
                        businessType: item.businessType,
                    }
                })
                item.showLineName = checked.map(item => item.name).join('、')
            })
            break
        case 'line':
            // 找到临时的响应式对象赋值
            state.temporaryObj.valuers = checked.map(item => {
                return {
                    name: item.name,
                    id: item.id,
                    identity: item._type,
                    _type: item._type,
                    treeType: item.treeType,
                    typeValue: item.typeValue,
                    businessType: item.businessType,
                }
            })
            // 回显一个input 框 纯文字
            state.temporaryObj.showLineName = checked.map(item => item.name).join('、')
            break
        default:
            break
    }
}

const delItem = async index => {
    const flag = await yConfirm('删除', '确定要删除吗？')
    if (flag) {
        props.data.rulesData.splice(index, 1)
    }
}

// 切换参与人
const personTypeChange = (e, index) => {
    console.log('index', index)
    props.data.rulesData[index].attendPeople = ''
    props.data.rulesData[index].participants = []
}

const onSelectChange = val => {
    state.selectedRowKeys = val
}

const orderType = {
    ascend: true,
    descend: false,
}

const handleChange = (pagination, filters, sorter) => {
    state.ruleSearch.isDesc = orderType[sorter.order]
    // state.ruleSearch.field = sorter.field
    getRuleList()
}

const getRuleList = () => {
    http.post('/cloud/evalPublicRule/page', {
        pageNo: 1,
        enable: 1,
        pageSize: 100,
        ...state.ruleSearch,
    }).then(res => {
        state.ruleData = res.data.list
    })
}

const inquireRule = () => {
    getRuleList()
}

const resetRule = () => {
    state.ruleSearch = {}
    getRuleList()
}

// 点击开始选择规则库里面的东西
const openRuleModal = (data, index) => {
    state.objIndex = index
    state.openRuleBox = true
    // 发送一个请求
    getRuleList()
}

const getTreeArr = id => {
    if (!id) return
    http.get('/cloud/evalPublicRule/get', { id: id }).then(res => {
        const tableData = res.data.firstIndicators.flatMap(item => {
            let data = []
            item.secondIndicators.forEach(secondIndicator => {
                data.push({
                    firstIndicatorId: item.id,
                    firstIndicatorName: item.name,
                    secondIndicatorId: secondIndicator.id,
                    secondIndicatorName: secondIndicator.name,
                    standardName: secondIndicator.indicatorScore.content,
                    startScore: secondIndicator.indicatorScore.minScore,
                    endScore: secondIndicator.indicatorScore.maxScore,
                })
            })
            return data
        })
        props.data.rulesData[state.objIndex].dataSource = tableData
        state.openRuleBox = false
    })
}

// 确定这个规则
// 记录id 回显示名字 用id 发请求去查详情 转一次数据再回显一次
const handleOkRule = () => {
    props.data.rulesData[state.objIndex].publicRuleId = state.selectedRowKeys.toString()
    getTreeArr(state.selectedRowKeys.toString())
}

const changeJifen = (value, option, record) => {
    record.scoreCardName = option.scoreCardName
}
</script>
<style scoped lang="less">
.step_two_box {
    position: relative;
    padding-bottom: 20px;
    border-bottom: 1px dashed #d9d9d9;
    .ScoringCriteriaAdd {
        width: 20px;
        height: 20px;
        cursor: pointer;

        &.editFir {
            display: none;
        }
    }

    .iconcenter {
        display: flex;
        align-items: center;
    }

    .ScoringCriteriaAdd + .ScoringCriteriaAdd {
        margin-left: 8px;
    }
    .ScoringCriteria_title {
        display: flex;
        justify-content: space-between;

        &:hover .ScoringCriteriaAdd {
            display: block;
        }
    }
    .delIcon {
        position: absolute;
        color: #ff4d4f;
        text-align: center;
        cursor: pointer;
        right: -90px;
        top: 50%;
    }
}

.step_two_box {
    :deep(.ant-radio-wrapper) {
        min-width: 100px;
    }
}

.tableBox {
    padding-top: 24px;
}
</style>
