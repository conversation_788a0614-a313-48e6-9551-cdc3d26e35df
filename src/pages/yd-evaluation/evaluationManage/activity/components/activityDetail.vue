<template>
    <YDrawer v-model:open="state.open" title="评价活动详情" :footer="null">
        <div class="container_box">
            <div class="top_box">
                <div class="left"><img :src="state.infoObj.promotionalImage" /></div>
                <div class="right">
                    <div class="r_top">
                        <div class="top_l">
                            <span
                                class="status_box"
                                :style="{
                                    borderColor: colorStatus[state.infoObj.status],
                                    color: colorStatus[state.infoObj.status],
                                }"
                            >
                                {{ textStatus[state.infoObj.status] }}
                            </span>
                            <span class="msg_text">
                                {{ state.infoObj.title }}
                            </span>
                        </div>
                        <div class="top_r">
                            活动状态:
                            <a-badge :color="getenableObj(state.infoObj.enable).color" :text="getenableObj(state.infoObj.enable).text" />
                            <a class="btn-link-color" ml-10 v-if="state.infoObj.edit && !state.infoObj.enable" @click="triggerParentEvent">
                                编辑
                            </a>
                        </div>
                    </div>
                    <div mt-15 mb-8>
                        <a class="btn-link-color" @click="recommend">查看活动介绍</a>
                    </div>
                    <a-row>
                        <a-col mt-13 display-flex :span="24">
                            <span class="l_label">活动时间：</span>
                            <span class="r_name">{{ `${state.infoObj.startDate || ''} ~ ${state.infoObj.endDate || ''}` }}</span>
                        </a-col>
                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">参与人员：</span>
                            <span class="r_name" :title="state.infoObj?.names?.join('、')">
                                {{ sliceName(state.infoObj?.names?.join('、')) || '-' }}
                            </span>
                        </a-col>
                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">参与人数：</span>
                            <span class="r_name">
                                {{ state.infoObj.partakePersonNum ? `${state.infoObj.partakePersonNum}人` : '-' }}
                            </span>
                        </a-col>
                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">评价规则：</span>
                            <a class="btn-link-color" @click="regulation">{{ '查看规则' }}</a>
                        </a-col>

                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">活动状态：</span>
                            <span class="r_name">{{ state.infoObj.enable ? '启用' : '禁用' }}</span>
                        </a-col>

                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">评价周期：</span>
                            <span class="r_name">{{ getcycleObj(state.infoObj.cycle) }}</span>
                        </a-col>

                        <!-- <a-col mt-13 display-flex :span="8">
                            <span class="l_label">评价范围：</span>
                            <span class="r_name">{{ 'XXXXXXXX' }}</span>
                        </a-col> -->

                        <!-- <a-col mt-13 display-flex :span="8">
                            <span class="l_label">评价方式：</span>
                            <span class="r_name">{{ 'XXXXXXXX' }}</span>
                        </a-col> -->

                        <!-- <a-col mt-13 display-flex :span="8">
                            <span class="l_label">评价结果：</span>
                            <span class="r_name">{{ state.infoObj.resultSetting || "-" }}</span>
                        </a-col> -->

                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">积分规则：</span>
                            <span class="r_name" :title="jifenRuleOptions[state.infoObj.settlementType]">
                                {{ jifenRuleOptions[state.infoObj.settlementType] || '-' }}
                            </span>
                        </a-col>

                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">创建人：</span>
                            <span class="r_name">{{ state.infoObj.createBy }}</span>
                        </a-col>

                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">创建时间：</span>
                            <span class="r_name">{{ state.infoObj.createTime }}</span>
                        </a-col>

                        <a-col mt-13 display-flex :span="8">
                            <span class="l_label">评价次数：</span>
                            <span class="r_name">{{ state.infoObj.evalMaxNum }}</span>
                        </a-col>
                    </a-row>
                </div>
            </div>
            <div>
                <div font-size-18 font-bold mt-24>参与人员:</div>
                <searchForm
                    mt-16
                    mb-16
                    v-model:formState="query"
                    :formList="state.formList"
                    @submit="reqGetList"
                    @reset="getReset"
                ></searchForm>
                <ETable
                    colSetting
                    hash="evaluationManage_activityDetail"
                    :data-source="page.list"
                    :columns="columns"
                    :total="page.total"
                    @paginationChange="paginationChange"
                    :current="page.pageNo"
                    @change="handleTableChange"
                >
                    <!-- <a-table-column title="姓名" data-index="toPersonName" key="toPersonName" />
                    <a-table-column title="班级" data-index="orgName" key="orgName" v-if="state.personType === 0" />
                    <a-table-column title="部门" data-index="orgName" key="orgName" v-if="state.personType === 1" />
                    <a-table-column title="参与次数" data-index="thisCount" key="thisCount" />
                    <a-table-column title="最高得分" data-index="maxScore" key="maxScore" />
                    <a-table-column title="最后得分" data-index="score" key="score" />
                    <a-table-column title="最新评价时间" data-index="scoreTime" key="scoreTime" />
                    <a-table-column title="操作" data-index="operate" key="operate" fixed="right" :width="150" /> -->
                    <template #thisCount="{ text, record }">{{ record.thisCount || 0 }}/{{ record.totalCount || 0 }}</template>
                    <template #operate="{ record }">
                        <a class="btn-link-color" mr-5 @click="handleEdit(record, false)">查看评价</a>
                        <!-- 只有operationFlag等于1   status等于1 才可以进行立即评价 -->
                        <a
                            class="btn-link-color"
                            mr-5
                            @click="handleEdit(record, true)"
                            v-if="record.operationFlag === 1 && state.infoObj.status === 1"
                        >
                            立即评价
                        </a>
                    </template>
                </ETable>
            </div>
        </div>
    </YDrawer>
    <EvaluationDetailed ref="evaluationDetailedRef" />

    <!-- 这里放一个活动介绍的富文本弹框让他显示一下富文本内容 -->
    <a-modal
        v-model:open="state.introduce"
        title="活动介绍"
        :footer="null"
        :width="700"
        :destroyOnClose="true"
        :keyboard="false"
        :maskClosable="false"
    >
        <div p-20>
            <Editor v-model="state.infoObj.description" disabled />
        </div>
    </a-modal>

    <!-- 这里放一个评价规则的弹窗 里面渲染 第二步创建时的table rules这个字段 -->
    <a-modal
        v-model:open="state.ruleModel"
        title="评价规则"
        :footer="null"
        :width="1000"
        :destroyOnClose="true"
        :keyboard="false"
        :maskClosable="false"
        :bodyStyle="{ overflow: 'auto', height: '550px' }"
    >
        <div v-if="!!state.rulesArr.length">
            <div v-for="(item, index) in state.rulesArr" :key="index" p-20>
                <div pb-12>
                    评价规则名称:
                    <span>{{ item.name }}</span>
                </div>
                <div>
                    <a-table :dataSource="item.dataSource" bordered :pagination="false">
                        <a-table-column
                            title="一级指标"
                            data-index="firstIndicatorName"
                            key="firstIndicatorName"
                            :customCell="
                                (record, rowIndex, column) => {
                                    let index = 1
                                    for (let i = rowIndex + 1; i < item.dataSource.length; i++) {
                                        const element = item.dataSource[i]
                                        if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                                            index = index + 1
                                        }
                                    }
                                    if (rowIndex > 0) {
                                        record['firstIndicatorId'] == item.dataSource[rowIndex - 1]?.firstIndicatorId && (index = 0)
                                    }
                                    return { rowSpan: index }
                                }
                            "
                        />
                        <a-table-column title="二级指标" data-index="secondIndicatorName" key="secondIndicatorName" />
                        <a-table-column title="评分标准" data-index="content" key="content" />
                        <a-table-column title="评分范围" data-index="range" key="range" :width="100" />
                        <a-table-column title="评价者" data-index="evaluator" key="evaluator" :width="300" />
                        <template #bodyCell="{ column, record, index }">
                            <template v-if="column.key === 'range'">
                                <div class="flex justify-center">
                                    <span>{{ record.minScore }}</span>
                                    <span>-</span>
                                    <span>{{ record.maxScore }}</span>
                                </div>
                            </template>
                            <template v-if="column.key === 'evaluator'">
                                <div>
                                    <span>{{ record.evaluator }}</span>
                                </div>
                            </template>
                        </template>
                    </a-table>
                </div>
            </div>
        </div>
        <div class="empty" v-else>
            <img class="empty-img" src="@/assets/images/library/empty.png" alt />
            <div>
                暂未添加规则,
                <span class="addRule" @click="addRule" v-if="state.infoObj.edit && !state.infoObj.enable">立即添加</span>
            </div>
        </div>
    </a-modal>
</template>
<script setup>
import EvaluationDetailed from '../../components/evaluationDetailed.vue'
import Editor from './Editor.vue'
let { query, page, getList, reset, paginationChange } = useList('/cloud/evalDayRulePerson/selectDayPersonScoreInfoPage')

const jifenRuleOptions = {
    1: '立即到账：每评价一次获得1分加1分',
    2: '活动结束后到账：按平均分，计算方式为：每次评分总和÷总参与次数=最终得分',
}

const evaluationDetailedRef = ref(null)
const emit = defineEmits(['childEvent'])
const state = reactive({
    activityId: '',
    open: false,
    personType: 0,
    introduce: false,
    ruleModel: false,
    img: '',
    infoObj: {
        approves: [],
    },
    rulesArr: [],
    formList: [
        {
            type: 'input',
            value: 'toPersonName',
            label: '姓名',
            span: 6,
        },
        {
            type: 'input',
            value: 'classesName',
            label: '班级',
            span: 6,
            // list: [
            //     { label: '全部', value: null },
            //     { label: '未开始', value: 0 },
            //     { label: '进行中', value: 1 },
            //     { label: '已结束', value: 2 },
            // ],
        },
    ],
})

const colorStatus = {
    0: '#FDB500',
    1: '#00C088',
    2: '#595959',
    null: '#FDB500',
}

const textStatus = {
    0: '未开始',
    1: '进行中',
    2: '已结束',
    null: '未开始',
}

const enableObj = {
    0: {
        text: '禁用',
        color: '#595959',
    },
    1: {
        text: '启用',
        color: '#00C088',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getenableObj(key) {
    return enableObj[key] || enableObj.default
}

const cycleObj = {
    0: '每天',
    1: '每周',
    2: '每月',
    default: '-', // 默认值存储在 default 属性中
}

function getcycleObj(key) {
    return cycleObj[key] || cycleObj.default
}

const columns = ref([
    { title: '姓名', dataIndex: 'toPersonName' },
    { title: '班级', dataIndex: 'orgName' },
    { title: '参与次数 ', dataIndex: 'thisCount' },
    { title: '最高得分', dataIndex: 'maxScore' },
    { title: '最后得分', dataIndex: 'score' },
    {
        title: '最新评价时间',
        dataIndex: 'scoreTime',
        // sorter: true,
        width: 140,
    },
    { title: '操作', dataIndex: 'operate', fixed: 'right', width: 150 },
])

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]
    query.field = sorter.field
    // getList()
}

const handleEdit = (item, isEdit) => {
    const params = {
        activityId: item.activityId,
        toPersonId: item.toPersonId,
        expandedKeys: [item.toPersonId],
        rulePersonId: item.id,
        toPersonName: item.toPersonName,
        queryThisFrom: isEdit ? true : null,
    }
    evaluationDetailedRef.value.showDetailedModel(params, isEdit, false, true)
}

const showModel = item => {
    state.activityId = item.id
    state.open = true
    getInfoObj()
    getReset()
    // getList({
    //     activityId: state.activityId,
    // })
}

// 获取详情的数据
const getInfoObj = () => {
    http.get('/cloud/evalActivity/get', {
        id: state.activityId,
    }).then(res => {
        state.personType = res.data?.rules[0]?.personType || 0 // 人员类型 默认是学生 0学生 1老师
        if (state.personType === 1) {
            state.formList[1].label = '部门'
            columns.value = [
                { title: '姓名', dataIndex: 'toPersonName' },
                { title: '部门', dataIndex: 'orgName' },
                { title: '参与次数 ', dataIndex: 'thisCount' },
                { title: '最高得分', dataIndex: 'maxScore' },
                { title: '最后得分', dataIndex: 'score' },
                {
                    title: '最新评价时间',
                    dataIndex: 'scoreTime',
                    // sorter: true,
                    width: 140,
                },
                { title: '操作', dataIndex: 'operate', fixed: 'right', width: 150 },
            ]
        }
        if (state.personType === 0) {
            state.formList[1].label = '班级'
            columns.value = [
                { title: '姓名', dataIndex: 'toPersonName' },
                { title: '班级', dataIndex: 'orgName' },
                { title: '参与次数 ', dataIndex: 'thisCount' },
                { title: '最高得分', dataIndex: 'maxScore' },
                { title: '最后得分', dataIndex: 'score' },
                {
                    title: '最新评价时间',
                    dataIndex: 'scoreTime',
                    // sorter: true,
                    width: 140,
                },
                { title: '操作', dataIndex: 'operate', fixed: 'right', width: 150 },
            ]
        }
        state.infoObj = res.data
        // console.log('规则的数组', state.infoObj.rules)
        //这里有一个rules数组,是规则的数组,把这里处理成多个table展示出来是关键
        // console.log('state.infoObj ', state.infoObj)

        state.rulesArr = res.data.rules.flatMap(item => {
            let data = {
                name: item.name, // 规则名称
                participants: item.participants, // 人的数组
                attendPeople: item.participants.map(item => item.name).join('、'), // 弄一个字符串来显示一下啊
                personType: item.personType,
                dataSource: [], //table数据
            }
            item.firstIndicators.forEach(indicator => {
                indicator.secondIndicators.forEach(secondIndicator => {
                    data.dataSource.push({
                        firstIndicatorId: indicator.id,
                        firstIndicatorName: indicator.name,
                        secondIndicatorId: secondIndicator.id,
                        secondIndicatorName: secondIndicator.name,
                        content: secondIndicator.indicatorScore.content,
                        minScore: secondIndicator.indicatorScore.minScore,
                        maxScore: secondIndicator.indicatorScore.maxScore,
                        valuers: secondIndicator.valuers,
                        evaluator: secondIndicator.valuers.map(item => item.name).join('、'), // 转个字符串
                    })
                })
            })

            return data
        })
    })
}

// 编辑
const triggerParentEvent = () => {
    state.open = false //关一下自己的弹窗
    emit('childEvent', true, {
        id: state.infoObj.id,
    })
}

const addRule = () => {
    state.open = false
    state.ruleModel = false
    emit('childEvent', true, {
        id: state.infoObj.id,
    })
}

const recommend = () => {
    state.introduce = true
    // YMessage.warning('等会等会')
}

const getReset = () => {
    reset({
        activityId: state.activityId,
    })
}

const reqGetList = () => {
    getList({
        activityId: state.activityId,
    })
}

const regulation = () => {
    state.ruleModel = true
}

watch(
    () => evaluationDetailedRef?.value?.state?.open,
    val => {
        if (!val) {
            getReset()
        }
    },
    {
        immediate: true,
        deep: true,
    },
)

// 超出长度截取
function sliceName(value) {
    return value?.length > 10 ? `${value.slice(0, 15)}...` : value
}

defineExpose({
    showModel,
})
</script>
<style scoped lang="less">
.container_box {
    padding-top: 20px;
    .top_box {
        background-color: #ebfaf5;
        display: flex;
        padding: 24px 16px;
        .left {
            img {
                width: 340px;
                height: 190px;
            }
        }
        .right {
            padding-left: 15px;
            flex: 1;
            .r_top {
                display: flex;
                .top_l {
                    display: flex;
                    flex: 1;
                    .status_box {
                        border: 1px solid #fdb500;
                        background: #fff;
                        padding: 0px 6px;
                        border-radius: 4px;
                        margin-right: 10px;
                    }
                    .msg_text {
                        flex: 1;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-weight: bold;
                        font-size: 16px;
                    }
                }
                .top_r {
                    text-align: right;
                    width: 170px;
                    .drop {
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                    }
                }
            }
            .l_label {
                color: #8c8c8c;
                margin-right: 10px;
                flex-shrink: 0;
            }
            .r_name {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}

.empty {
    text-align: center;
    padding-top: 80px;

    .empty-img {
        width: 180px;
        height: 180px;
    }
    .addRule {
        color: #00c088;
        cursor: pointer;
    }
}
</style>
