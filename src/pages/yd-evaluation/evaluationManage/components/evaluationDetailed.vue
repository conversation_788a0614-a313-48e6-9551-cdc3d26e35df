<template>
    <YDrawer class="drawer_box" v-model:open="state.open" :title="state.isEdit ? '立即评价' : '查看评价'" @closebtn="handleClose">
        <a-spin :spinning="spinning">
            <div class="evaluationDetailedPage" h-full flex>
                <div v-if="!state.isRecordDetail" class="treeListBox" h-full flex flex-col>
                    <div p-b-16>
                        <a-input-search placeholder="请输入姓名" v-model:value="state.toPersonName" @search="searchDevice" />
                    </div>
                    <div flex-auto overflow-y-auto class="tree_box" v-if="state.treeData.length">
                        <a-tree
                            block-node
                            :virtual="true"
                            :defaultExpandAll="true"
                            :autoExpandParent="true"
                            v-model:selectedKeys="selectedKeysArr"
                            :fieldNames="{
                                children: 'children',
                                title: 'name',
                                key: 'id',
                            }"
                            @select="handleSelect"
                            :tree-data="state.treeData"
                        >
                            <template #title="{ name, operationFlag, levelType }">
                                <div :style="{ color: color[operationFlag] }">
                                    <span
                                        v-if="levelType === 2 && operationFlag !== null && operationFlag !== 0"
                                        class="status_box"
                                        :style="{
                                            color: color[operationFlag],
                                            borderColor: color[operationFlag],
                                        }"
                                    >
                                        {{ operationFlag === 1 ? '未评' : '已评' }}
                                    </span>
                                    {{ name }}
                                </div>
                            </template>
                        </a-tree>
                    </div>
                </div>
                <div v-else class="empty-data">暂无数据</div>
                <div class="appraiseTable">
                    <div v-if="state.isRecordDetail" class="flex h-42 line-height-42 bg-#e8f8f3 mb-20 pl-15 justify-evenly">
                        <div>
                            <span font-bold>评价类型:</span>
                            {{ state.titleMsg.evalTypeName }}
                        </div>
                        <div>
                            <span font-bold>活动名称:</span>
                            {{ state.titleMsg.title }}
                        </div>
                        <div>
                            <span font-bold>总获得积分:</span>
                            {{ state.titleMsg.totalScore }}
                        </div>
                    </div>
                    <div class="historyTitle">评价历史</div>
                    <div>
                        <ETable
                            :loading="page.loading"
                            :dataSource="page.list"
                            :total="page.total"
                            @paginationChange="paginationChange"
                            :current="query.pageNo"
                        >
                            <a-table-column title="姓名" data-index="toPersonName" />
                            <a-table-column title="班级" data-index="orgName"></a-table-column>
                            <a-table-column title="参与次数" data-index="thisCount" :width="100">
                                <template #default="{ record }">{{ record.thisCount || 0 }}/{{ record.totalCount || 0 }}</template>
                            </a-table-column>
                            <a-table-column title="最后得分" data-index="score" :width="100"></a-table-column>
                            <a-table-column title="最新评价时间" data-index="scoreTime" :width="160"></a-table-column>
                            <a-table-column title="操作" data-index="operate">
                                <template #default="{ record }">
                                    <a-button type="link" class="btn-link-color" @click="appraisalDetails(record)">评价详情</a-button>
                                    <a-button type="link" class="btn-link-color" @click="appraisalRecord(record)">评价记录</a-button>
                                </template>
                            </a-table-column>
                        </ETable>
                    </div>
                    <!-- 这块放一个打分的模块 -->
                    <div v-if="state.isEdit" class="markBox mt-20">
                        <div class="markTitleBox">
                            <div class="markLine"></div>
                            <div class="markTitle">第{{ state.partakeCount }}周期评价</div>
                        </div>

                        <div class="markTable">
                            <a-table
                                :scroll="{ x: 1500 }"
                                :dataSource="state.tableList"
                                :columns="tableListColumns"
                                bordered
                                :pagination="false"
                            >
                                <template #bodyCell="{ column, record }">
                                    <template v-if="column.key === 'minScore'">{{ record.minScore }}-{{ record.maxScore }}</template>
                                    <template v-if="column.key === 'newestScore'">
                                        <div v-if="record.evalScoreTypeList && record.evalScoreTypeList.includes('score')">
                                            <a-input-number
                                                v-model:value="record.newestScore"
                                                :precision="2"
                                                :min="record.minScore"
                                                :max="record.maxScore"
                                                :step="state.rulePersonDetails.minRatingStep || 1"
                                                placeholder="请评分"
                                            />
                                        </div>
                                        <div v-else>-</div>
                                    </template>
                                    <template v-if="column.key === 'totalIndicatorScore_new'">{{ lastCount(record) }}</template>

                                    <template v-if="column.key === 'comment'">
                                        <!-- 评语输入框 - 当evalScoreTypeList包含'text'时显示 -->
                                        <div v-if="record.evalScoreTypeList && record.evalScoreTypeList.includes('text')">
                                            <a-textarea v-model:value="record.comment" placeholder="请输入评语" :maxlength="200" />
                                        </div>
                                        <div v-else>-</div>
                                    </template>
                                    <template v-if="column.key === 'Paths'">
                                        <!-- 图片/视频上传和显示 -->
                                        <div class="paths-container">
                                            <!-- 统一上传按钮 -->
                                            <div
                                                class="upload-buttons"
                                                v-if="
                                                    record.evalScoreTypeList &&
                                                    (record.evalScoreTypeList.includes('image') ||
                                                        record.evalScoreTypeList.includes('video'))
                                                "
                                            >
                                                <a-upload
                                                    :file-list="[]"
                                                    :before-upload="file => handleFileUpload(file, record)"
                                                    :accept="getAcceptTypes(record.evalScoreTypeList)"
                                                    :show-upload-list="false"
                                                    multiple
                                                >
                                                    <a-button size="small" type="primary" ghost>
                                                        <template #icon>
                                                            <CloudUploadOutlined />
                                                        </template>
                                                        {{ getUploadButtonText(record.evalScoreTypeList) }}
                                                    </a-button>
                                                </a-upload>
                                            </div>

                                            <!-- 缩略图显示区域 - 只显示第一个文件 -->
                                            <div class="thumbnail-container" style="margin-top: 8px">
                                                <div
                                                    v-if="getFirstMediaFile(record)"
                                                    class="thumbnail-item"
                                                    @click="openPreviewModal(record)"
                                                >
                                                    <!-- 图片缩略图 -->
                                                    <img
                                                        v-if="getFirstMediaFile(record).type === 'image'"
                                                        :src="getFirstMediaFile(record).url"
                                                        alt="缩略图"
                                                    />
                                                    <!-- 视频缩略图 -->
                                                    <video
                                                        v-else-if="getFirstMediaFile(record).type === 'video'"
                                                        :src="getFirstMediaFile(record).url"
                                                    ></video>

                                                    <div class="thumbnail-overlay">
                                                        <EyeOutlined v-if="getFirstMediaFile(record).type === 'image'" />
                                                        <PlayCircleOutlined v-else />
                                                    </div>

                                                    <!-- 多文件指示器 -->
                                                    <div v-if="getTotalMediaCount(record) > 1" class="media-count-badge">
                                                        +{{ getTotalMediaCount(record) - 1 }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </a-table>
                        </div>

                        <div class="finalcoreBox">
                            <span class="red">*</span>
                            最终得分
                            <span class="finalcore">{{ totalScore }}</span>
                            分
                        </div>
                    </div>
                </div>
            </div>
        </a-spin>
        <template #footer v-if="state.isEdit">
            <a-button mr-5 @click="handleClose">取消</a-button>
            <a-button type="primary" @click="handleConfirm">确定</a-button>
        </template>
    </YDrawer>

    <!-- 这里放一个评价详情的modal  -->
    <a-modal
        v-model:open="state.openDetails"
        width="1500px"
        title="评价详情"
        :footer="null"
        :bodyStyle="{ padding: '16px', overflow: 'auto', maxHeight: '550px' }"
    >
        <div>
            <div class="mb-10 font-14 font-700">第{{ state.partakeCount }}周期评价</div>
            <div class="detailsTableBox_table">
                <a-table :dataSource="state.modelData" :columns="modelDataColumns" bordered :pagination="false">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'minScore'">{{ record.minScore }}-{{ record.maxScore }}</template>
                    </template>
                </a-table>
            </div>
        </div>
    </a-modal>
    <a-modal
        v-model:open="state.openRecordDetails"
        width="1500px"
        title="评价记录"
        :footer="null"
        :bodyStyle="{ padding: '16px', overflow: 'auto', maxHeight: '550px' }"
    >
        <div>
            <div class="mb-10 font-14 font-700">第{{ state.partakeCount }}周期评价</div>
            <div class="detailsTableBox_table">
                <a-table :dataSource="state.modelRecordData" :columns="markRecordColumns" bordered :pagination="false">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'minScore'">{{ record.minScore }}-{{ record.maxScore }}</template>
                        <template v-if="column.key === 'score'">
                            <div
                                :style="{
                                    margin: '-16px',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                }"
                            >
                                <div
                                    :style="{
                                        flex: 1,
                                        borderBottom: index == record.scoreRecordList.length - 1 ? 'none' : '1px solid #f0f0f0',
                                        display: 'flex',
                                        padding: '16px',
                                        alignItems: 'center',
                                    }"
                                    v-for="(item, index) in record.scoreRecordList"
                                    :key="index"
                                >
                                    {{ item.score }}
                                </div>
                            </div>
                        </template>
                        <template v-if="column.key === 'scoreTime'">
                            <div
                                :style="{
                                    margin: '-16px',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                }"
                            >
                                <div
                                    :style="{
                                        flex: 1,
                                        borderBottom: index == record.scoreRecordList.length - 1 ? 'none' : '1px solid #f0f0f0',
                                        display: 'flex',
                                        padding: '16px',
                                        alignItems: 'center',
                                    }"
                                    v-for="(item, index) in record.scoreRecordList"
                                    :key="index"
                                >
                                    {{ item.scoreTime }}
                                </div>
                            </div>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
    </a-modal>

    <!-- 媒体预览组件 -->
    <MediaPreviewModal
        :isDelete="state.isEdit ? true : false"
        v-model:open="previewModal.open"
        :img-paths="previewModal.imgPaths"
        :video-paths="previewModal.videoPaths"
        @delete-media="handleDeleteMedia"
    />
</template>
<script setup name="evaluationDetailed">
// 引入图标组件
import { CloudUploadOutlined, EyeOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'

// 引入组件
import MediaPreviewModal from './MediaPreviewModal.vue'

// 定义emit事件，用于向父组件传递关闭事件
const emit = defineEmits(['drawerClose'])

const color = {
    0: '#000000D8',
    1: '#000000D8',
    2: '#00B781',
}
const selectedKeysArr = ref([])
const spinning = ref(false)
const state = reactive({
    open: false,
    openRecordDetails: false,
    openDetails: false,
    isEdit: false,
    isRecordDetail: false,
    toPersonName: '',
    treeData: [],
    tableList: [],
    modelData: [],
    modelRecordData: [],
    partakeCount: 0,
    expandedKeys: [],
    operationFlag: 1,
    titleMsg: {
        evalTypeName: '',
        title: '',
        totalScore: '',
    },
    rulePersonDetails: {},
})

// 预览Modal状态
const previewModal = reactive({
    open: false,
    imgPaths: '', // 图片路径字符串
    videoPaths: '', // 视频路径字符串
    currentRecord: null, // 当前操作的记录
})

let { query, page, getList, reset, paginationChange } = useList('/cloud/evalDayRulePerson/pageDayPersonScore')

function findNameById(tree, id) {
    // 遍历树形结构数组
    for (let node of tree) {
        // 如果当前节点的id等于要查找的id，则返回当前节点的name
        if (node.toPersonId === id) {
            return node.id
        }
        // 如果当前节点有子节点，则递归调用函数在子节点中查找
        if (node.children) {
            let result = findNameById(node.children, id)
            // 如果在子节点中找到了对应id的name，则直接返回结果
            if (result) {
                return result
            }
        }
    }
    // 如果遍历完整个树仍未找到对应id的name，则返回null或者其他自定义的值
    return null
}

// 树查询
const searchDevice = select => {
    spinning.value = true
    const params = {
        activityId: query.activityId,
        toPersonName: state.toPersonName,
        queryThisFrom: query.queryThisFrom,
    }
    http.post('/cloud/evalDayRulePerson/getDayRulePersonTerr', params).then(res => {
        state.treeData = res.data
        spinning.value = false
        if (!!select) {
            const selectId = findNameById(state.treeData, select)
            selectedKeysArr.value = [selectId]
            nextTick(() => {
                if (document.getElementsByClassName('ant-tree-treenode-selected').length > 0) {
                    document.getElementsByClassName('ant-tree-treenode-selected')[0].scrollIntoView()
                }
            })
        }
        getList()
    })
}

// 最后得分
const lastCount = record => {
    if (state.rulePersonDetails.settlementType === 1) {
        // 如果是1立即结算 打多少就是多少 本次是多少就是多少分 优先前端输入的值 没有前端输入的值回显后端的值
        record.finalScore = record.newestScore || 0
        return record.newestScore || 0
    } else {
        // 如果是2活动结束后结算
        let finalScore = 0
        const othersIndicatorScore = record.othersIndicatorScore || 0
        const newestScore = record.newestScore || 0
        const evaluatePersonNum = record.evaluatePersonNum || 0
        const thisIndicatorScore = record.thisIndicatorScore || 0
        const thisTotalScoreCount = record.thisTotalScoreCount || 0 // 我自己所有的总分
        if (Array.isArray(record.scoreRecordList) && record.scoreRecordList.length > 0) {
            //  如果自己已经有过打分
            //  别人的所有总分 + 自己的所有总分  / 总人数
            finalScore =
                (othersIndicatorScore * evaluatePersonNum + (newestScore + thisTotalScoreCount)) /
                (record.scoreRecordList.length + 1) /
                (evaluatePersonNum + 1)
        } else {
            // 如果自己从来没有打过分 是第一次来打分
            finalScore = (othersIndicatorScore * evaluatePersonNum + newestScore) / (evaluatePersonNum + 1)
        }
        record.finalScore = finalScore
        return +finalScore.toFixed(2) || 0
    }
}

// 合计得分为最后得分的总值
// 把数组循环出来 相加就行了
let totalScore = computed(() => {
    // 假设 state.tableList 是包含对象的数组
    const totalScoreSum = state.tableList.reduce((accumulator, currentItem) => {
        const finalScore = currentItem.finalScore || 0
        if (finalScore) {
            return accumulator + Number(finalScore)
        } else {
            return accumulator
        }
    }, 0)

    return totalScoreSum ? +totalScoreSum.toFixed(2) : 0
})

// 获取评价详情
const getRulePersonScoreList = ({ activityId, toPersonId, ...args }) => {
    const params = {
        activityId,
        toPersonId,
        ...args,
    }
    return new Promise((reslove, reject) => {
        http.post('/cloud/evalDayRulePerson/getRulePersonScoreList', params).then(res => {
            const tableData = res.data.flatMap(item => {
                let data = []
                item.secondIndicators.forEach(secondIndicator => {
                    data.push({
                        firstIndicatorId: item.id,
                        firstIndicatorName: item.name,
                        secondIndicatorName: secondIndicator.name,
                        content: secondIndicator.indicatorScore.content,
                        minScore: secondIndicator.indicatorScore.minScore,
                        maxScore: secondIndicator.indicatorScore.maxScore,
                        scoreCardName: secondIndicator.indicatorScore.scoreCardName,
                        evalScoreTypeList: secondIndicator.indicatorScore.evalScoreTypeList,
                        othersIndicatorScore: secondIndicator.indicatorScore.othersIndicatorScore,
                        thisIndicatorScore: secondIndicator.indicatorScore.thisIndicatorScore,
                        totalIndicatorScore: secondIndicator.indicatorScore.totalIndicatorScore,
                        othersTotalScore: secondIndicator.indicatorScore.othersTotalScore,
                        evaluatePersonNum: secondIndicator.indicatorScore.evaluatePersonNum,
                        scoreRecordList: secondIndicator.indicatorScore.scoreRecordList,

                        id: secondIndicator.id,

                        // 新字段
                        othersTotalScoreCount: secondIndicator.indicatorScore.othersTotalScoreCount,
                        thisTotalScoreCount: secondIndicator.indicatorScore.thisTotalScoreCount, // 我自己所有的总分
                        comment: secondIndicator.indicatorScore.comment, // 评语
                        imgPaths: secondIndicator.indicatorScore.imgPaths, // 图片路径
                        videoPaths: secondIndicator.indicatorScore.videoPaths, // 视频路径
                    })
                })
                return data
            })
            reslove(tableData)
        })
    })
}

// 获取活动评分规则数据
const getEvalRulePersonDetails = rulePersonId => {
    http.post('/cloud/evalDayRulePerson/getEvalRulePersonDetails', {
        rulePersonId: rulePersonId,
    }).then(res => {
        console.log(res)
        state.rulePersonDetails = res.data
    })
}

// 树点击，禁止重复点击取消选中
const handleSelect = async (selectedKeys, e) => {
    // 如果出现“反选”（selectedKeys 为空），强制保持当前节点选中
    if (!selectedKeys || selectedKeys.length === 0) {
        const keepKey = e?.node?.key ?? e?.node?.id ?? e?.node?.dataRef?.id
        if (keepKey !== undefined && keepKey !== null) {
            selectedKeysArr.value = [keepKey]
        }
    } else {
        selectedKeysArr.value = selectedKeys
    }

    if (e.node.levelType !== 2) return
    state.partakeCount = e.node.partakeCount
    state.operationFlag = e.node.operationFlag
    query.toPersonId = e.node.toPersonId
    query.rulePersonId = e.node.rulePersonId
    getList()
    state.tableList = []
    const params = {
        activityId: e.node.activityId,
        toPersonId: e.node.toPersonId,
        queryThisFrom: true,
        rulePersonId: e.node.rulePersonId,
    }
    state.tableList = await getRulePersonScoreList({ ...params })
    state.tableList.forEach(item => {
        item.comment = ''
        item.imgPaths = ''
        item.videoPaths = ''
    })

    // 再查一个整个活动零零碎碎的其他配置信息
    getEvalRulePersonDetails(e.node.rulePersonId)
}

// 取消
const handleClose = () => {
    state.open = false
    state.tableList = []
    page.list = []
}

// 确定
const handleConfirm = async () => {
    // 校验是否全部填了评分
    if (
        state.tableList.some(item => {
            const score = item.newestScore
            // 检查 score 是否为 null、undefined 或者空字符串
            if (score === null || score === undefined || score === '') {
                return true
            }
            // 将 score 转换为数字
            const numScore = parseFloat(score)
            // 检查转换后的数字是否为 NaN
            return isNaN(numScore)
        })
    ) {
        YMessage.warning('还有未评分项，请评分')
        return
    }

    const flag = await yConfirm('提示', '确认提交本次评价？')
    // 找到下标直接删就完事了啊
    if (flag) {
        // 构建评分数据，包含评分、评语、图片和视频路径
        const scoringList = state.tableList.map(item => ({
            id: item.id,
            score: item.newestScore,
            comment: item.comment || '', // 评语
            imgPaths: item.imgPaths || '', // 图片路径
            videoPaths: item.videoPaths || '', // 视频路径
        }))

        await http.post('/cloud/evalDayRulePerson/evalAddScoring', { scoringList })
        YMessage.success('打分成功')
        searchDevice(query.toPersonId)
        // 直接刷新接口
        state.tableList = []
        state.tableList = await getRulePersonScoreList({ ...query })
        state.tableList.forEach(item => {
            item.comment = ''
            item.imgPaths = ''
            item.videoPaths = ''
        })
    }
}

// 评价详情 这里可以打开一个弹窗
const appraisalDetails = async item => {
    const params = {
        activityId: item.activityId,
        toPersonId: item.toPersonId,
        rulePersonId: item.rulePersonId,
    }
    state.partakeCount = item.thisCount
    state.modelData = await getRulePersonScoreList({ ...params })
    console.log(state.modelData, 'state.modelData')

    state.openDetails = true
}

const appraisalRecord = async item => {
    const params = {
        activityId: item.activityId,
        toPersonId: item.toPersonId,
        rulePersonId: item.rulePersonId,
    }
    state.partakeCount = item.thisCount
    state.modelRecordData = await getRulePersonScoreList({ ...params })
    state.openRecordDetails = true
}

// 打开查看评价的弹窗
const showDetailedModel = async (data, isEdit, isRecordDetail, isActivityDetail) => {
    state.isEdit = isEdit
    state.isRecordDetail = isRecordDetail
    state.expandedKeys = data.expandedKeys
    query.activityId = data.activityId
    query.toPersonId = data.toPersonId
    query.rulePersonId = data.rulePersonId
    query.queryThisFrom = data.queryThisFrom

    state.titleMsg = data
    if (!isRecordDetail) {
        searchDevice(query.toPersonId)
    }
    // 如果是从活动评价进入
    if (isActivityDetail || isRecordDetail) {
        getList()
    }
    if (isEdit && isActivityDetail) {
        state.tableList = await getRulePersonScoreList({ ...query })
        state.tableList.forEach(item => {
            item.comment = ''
            item.imgPaths = ''
            item.videoPaths = ''
        })
    }
    state.open = true
}

// 用于跟踪正在处理的文件，避免重复处理
const processingFiles = new Set()

// 文件上传处理（每个文件单独调用，但使用队列机制确保数据一致性）
const handleFileUpload = (file, record) => {
    // 生成文件唯一标识
    const fileId = `${file.name}_${file.size}_${file.lastModified}`

    // 如果文件正在处理中，跳过
    if (processingFiles.has(fileId)) {
        return false
    }

    // 标记文件为处理中
    processingFiles.add(fileId)

    // 文件类型验证
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')

    // 大小校验（单位 MB）
    // const sizeMB = file.size / 1024 / 1024
    // if (isImage && sizeMB > 5) {
    //     YMessage.error(`图片大小为 ${sizeMB.toFixed(2)}MB，超过 5MB 限制`)
    //     processingFiles.delete(fileId)
    //     return false
    // }
    // if (isVideo && sizeMB > 100) {
    //     YMessage.error(`视频大小为 ${sizeMB.toFixed(2)}MB，超过 100MB 限制`)
    //     processingFiles.delete(fileId)
    //     return false
    // }

    // 检查是否允许上传该类型文件
    const allowImage = record.evalScoreTypeList.includes('image')
    const allowVideo = record.evalScoreTypeList.includes('video')

    if (isImage && !allowImage) {
        YMessage.error('当前评分方式不支持上传图片')
        processingFiles.delete(fileId)
        return false
    }

    if (isVideo && !allowVideo) {
        YMessage.error('当前评分方式不支持上传视频')
        processingFiles.delete(fileId)
        return false
    }

    if (!isImage && !isVideo) {
        YMessage.error('只支持上传图片或视频文件')
        processingFiles.delete(fileId)
        return false
    }

    // 获取当前已有的文件数量（实时获取，确保准确性）
    const currentImageCount = record.imgPaths ? getImageList(record.imgPaths).length : 0
    const currentVideoCount = record.videoPaths ? getVideoList(record.videoPaths).length : 0

    // 严格的数量限制检查
    if (isImage && currentImageCount >= 9) {
        YMessage.error(`图片最多只能上传9个，当前已有${currentImageCount}个`)
        processingFiles.delete(fileId)
        return false
    }

    if (isVideo && currentVideoCount >= 1) {
        YMessage.error(`视频最多只能上传1个，当前已有${currentVideoCount}个`)
        processingFiles.delete(fileId)
        return false
    }

    // 上传文件
    http.form('/file/common/upload', { file, folderType: 'evalActivity' }, {})
        .then(res => {
            const fileUrl = res.data[0].url

            // 再次检查数量限制（防止并发上传导致的问题）
            const latestImageCount = record.imgPaths ? getImageList(record.imgPaths).length : 0
            const latestVideoCount = record.videoPaths ? getVideoList(record.videoPaths).length : 0

            if (isImage && latestImageCount >= 9) {
                // YMessage.error('图片数量已达上限，本次上传被取消')
                processingFiles.delete(fileId)
                return
            }

            if (isVideo && latestVideoCount >= 1) {
                // YMessage.error('视频数量已达上限，本次上传被取消')
                processingFiles.delete(fileId)
                return
            }

            // 根据文件类型判断存储到哪个字段
            if (isImage) {
                // 图片文件 - 确保不重复添加
                const existingImages = record.imgPaths ? getImageList(record.imgPaths) : []
                if (!existingImages.includes(fileUrl)) {
                    if (record.imgPaths) {
                        record.imgPaths += ',' + fileUrl
                    } else {
                        record.imgPaths = fileUrl
                    }
                    YMessage.success('图片上传成功')
                }
            } else if (isVideo) {
                // 视频文件 - 确保不重复添加
                const existingVideos = record.videoPaths ? getVideoList(record.videoPaths) : []
                if (!existingVideos.includes(fileUrl)) {
                    if (record.videoPaths) {
                        record.videoPaths += ',' + fileUrl
                    } else {
                        record.videoPaths = fileUrl
                    }
                    YMessage.success('视频上传成功')
                }
            }

            // 移除处理标记
            processingFiles.delete(fileId)
        })
        .catch(() => {
            YMessage.error('文件上传失败')
            // 移除处理标记
            processingFiles.delete(fileId)
        })

    return false // 阻止默认上传行为
}

// 获取图片列表
const getImageList = imgPaths => {
    if (!imgPaths) return []
    return imgPaths.split(',').filter(path => path.trim())
}

// 获取视频列表
const getVideoList = videoPaths => {
    if (!videoPaths) return []
    return videoPaths.split(',').filter(path => path.trim())
}

// 根据evalScoreTypeList获取accept类型
const getAcceptTypes = evalScoreTypeList => {
    const acceptTypes = []
    if (evalScoreTypeList.includes('image')) {
        acceptTypes.push('image/png,image/jpeg,image/jpg')
    }
    if (evalScoreTypeList.includes('video')) {
        acceptTypes.push('video/mp4')
    }
    return acceptTypes.join(',')
}

// 获取上传按钮文字
const getUploadButtonText = evalScoreTypeList => {
    const hasImage = evalScoreTypeList.includes('image')
    const hasVideo = evalScoreTypeList.includes('video')
    if (hasImage && hasVideo) {
        return '上传图片/视频'
    } else if (hasImage) {
        return '上传图片'
    } else if (hasVideo) {
        return '上传视频'
    }
    return '上传图片/视频'
}

// 获取第一个媒体文件
const getFirstMediaFile = record => {
    // 优先显示图片
    if (record.imgPaths) {
        const imageList = getImageList(record.imgPaths)
        if (imageList.length > 0) {
            return { type: 'image', url: imageList[0] }
        }
    }

    // 然后显示视频
    if (record.videoPaths) {
        const videoList = getVideoList(record.videoPaths)
        if (videoList.length > 0) {
            return { type: 'video', url: videoList[0] }
        }
    }

    return null
}

// 获取总媒体文件数量
const getTotalMediaCount = record => {
    const imageCount = record.imgPaths ? getImageList(record.imgPaths).length : 0
    const videoCount = record.videoPaths ? getVideoList(record.videoPaths).length : 0
    return imageCount + videoCount
}

// 打开预览Modal
const openPreviewModal = record => {
    previewModal.imgPaths = record.imgPaths || ''
    previewModal.videoPaths = record.videoPaths || ''
    previewModal.currentRecord = record // 保存当前记录的引用
    previewModal.open = true
}

// 处理删除媒体文件
const handleDeleteMedia = ({ type, url }) => {
    const record = previewModal.currentRecord
    if (!record) return

    try {
        if (type === 'image') {
            // 删除图片
            if (record.imgPaths) {
                const imageList = record.imgPaths.split(',').filter(path => path.trim())
                const updatedList = imageList.filter(path => path.trim() !== url)
                record.imgPaths = updatedList.join(',')

                // 更新预览Modal的数据
                previewModal.imgPaths = record.imgPaths
            }
        } else if (type === 'video') {
            // 删除视频
            if (record.videoPaths) {
                const videoList = record.videoPaths.split(',').filter(path => path.trim())
                const updatedList = videoList.filter(path => path.trim() !== url)
                record.videoPaths = updatedList.join(',')

                // 更新预览Modal的数据
                previewModal.videoPaths = record.videoPaths
            }
        }

        console.log('删除后的数据:', {
            imgPaths: record.imgPaths,
            videoPaths: record.videoPaths,
        })
    } catch (error) {
        console.error('删除媒体文件失败:', error)
        YMessage.error('删除失败')
    }
}

defineExpose({
    state,
    showDetailedModel,
})

// 打分模块表头
const tableListColumns = [
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
        customCell: (record, rowIndex, column) => {
            let index = 1
            for (let i = rowIndex + 1; i < state.tableList.length; i++) {
                const element = state.tableList[i]
                if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                    index = index + 1
                }
            }
            if (rowIndex > 0) {
                record['firstIndicatorId'] == state.tableList[rowIndex - 1]?.firstIndicatorId && (index = 0)
            }
            return { rowSpan: index }
        },
        width: 150,
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',
        key: 'secondIndicatorName',
        width: 150,
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
    },
    {
        title: '评分范围',
        dataIndex: 'minScore',
        key: 'minScore',
        width: 100,
    },
    {
        title: '积分卡',
        dataIndex: 'scoreCardName',
        key: 'scoreCardName',
        width: 100,
    },
    {
        title: '他人评分',
        dataIndex: 'othersIndicatorScore',
        key: 'othersIndicatorScore',
        width: 100,
        customRender: ({ text }) => text || '-',
    },
    {
        title: '本次评分',
        dataIndex: 'newestScore',
        key: 'newestScore',
        width: 120,
    },
    {
        title: '最后得分',
        dataIndex: 'totalIndicatorScore_new',
        key: 'totalIndicatorScore_new',
        width: 100,
        customHeaderCell: () => ({
            style: {
                background: '#FCF1D2',
            },
        }),
        customCell: () => ({
            style: {
                background: '#FCF1D2',
            },
        }),
    },
    {
        title: '评语',
        dataIndex: 'comment',
        key: 'comment',
        width: 250,
    },
    {
        title: '图片/视频',
        dataIndex: 'Paths',
        key: 'Paths',
        width: 150,
    },
]

// 这个是详情的表头
const modelDataColumns = [
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
        customCell: (record, rowIndex, column) => {
            let index = 1
            for (let i = rowIndex + 1; i < state.modelData.length; i++) {
                const element = state.modelData[i]
                if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                    index = index + 1
                }
            }
            if (rowIndex > 0) {
                record['firstIndicatorId'] == state.modelData[rowIndex - 1]?.firstIndicatorId && (index = 0)
            }
            return { rowSpan: index }
        },
        width: 200,
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',
        key: 'secondIndicatorName',
        width: 200,
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
    },
    {
        title: '评分范围',
        dataIndex: 'minScore',
        key: 'minScore',
        width: 100,
    },
    {
        title: '积分卡',
        dataIndex: 'scoreCardName',
        key: 'scoreCardName',
        width: 150,
    },
    {
        title: '他人评分',
        dataIndex: 'othersIndicatorScore',
        key: 'othersIndicatorScore',
        customRender: ({ text }) => text || '-',
        width: 100,
    },
    {
        title: '本次评分',
        dataIndex: 'thisIndicatorScore',
        key: 'thisIndicatorScore',
        width: 100,
    },
    {
        title: '最后得分',
        dataIndex: 'totalIndicatorScore',
        key: 'totalIndicatorScore',
        width: 100,
    },
]

const markRecordColumns = [
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
        width: '260px',
        customCell: (record, rowIndex, column) => {
            let index = 1
            for (let i = rowIndex + 1; i < state.modelRecordData.length; i++) {
                const element = state.modelRecordData[i]
                if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                    index = index + 1
                }
            }
            if (rowIndex > 0) {
                record['firstIndicatorId'] == state.modelRecordData[rowIndex - 1]?.firstIndicatorId && (index = 0)
            }
            return { rowSpan: index }
        },
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',
        width: '260px',
        key: 'secondIndicatorName',
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
        width: '380px',
    },
    {
        title: '评分范围',
        dataIndex: 'minScore',
        key: 'minScore',
        width: '380px',
    },
    {
        title: '评分',
        dataIndex: 'score',
        key: 'score',
        width: '100px',
    },
    {
        title: '评分时间',
        dataIndex: 'scoreTime',
        key: 'scoreTime',
        width: '230px',
    },
]
</script>

<style lang="less" scoped>
.drawer_box {
    .evaluationDetailedPage {
        .treeListBox {
            position: fixed;
            height: calc(100% - 250px);
            flex: 0 0 200px;
            width: 200px;
            overflow: hidden;

            padding-top: 16px;
            padding-right: 16px;

            .tree_box {
                :deep(.ant-tree-treenode) {
                    width: 100%;
                    padding: 6px 0px 6px 0px !important;
                    position: relative;
                    &:hover {
                        background-color: #f6f6f6;
                    }
                    &.ant-tree-treenode-selected {
                        background-color: #e8f8f3;
                        .ant-tree-switcher {
                            color: #00b781;
                        }
                        .ant-tree-title {
                            color: #00b781;
                        }
                        .tree-node-action-button {
                            color: #00b781;
                        }
                    }
                }
            }

            .status_box {
                border: 1px solid;
                border-radius: 4px;
                padding: 1px 3px;
            }
        }

        .empty-data {
            position: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 200px;
            color: rgba(0, 0, 0, 0.25);
            height: 70%;
        }

        .appraiseTable {
            overflow-y: hidden;
            margin-left: 200px;
            flex: auto;
            padding: 16px 0px 0px 16px;
            .historyTitle {
                padding-bottom: 12px;
                font-weight: 600;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
                text-align: left;
                font-style: normal;
            }
        }
    }

    .markBox {
        background: #eaf9f5;
        padding: 16px;
        .markTitleBox {
            display: flex;
            padding-bottom: 12px;
            align-items: center;
            .markLine {
                width: 2px;
                height: 16px;
                background: #00b781;
            }
            .markTitle {
                font-weight: 600;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
                padding-left: 8px;
            }
        }
    }
}

.finalcoreBox {
    padding-top: 24px;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    .red {
        color: #f5222d;
    }
    .finalcore {
        font-weight: 400;
        font-size: 32px;
        color: #333333;
    }
}

// 图片/视频相关样式
.paths-container {
    .upload-buttons {
        display: flex;
        gap: 8px;
    }

    .thumbnail-container {
        .thumbnail-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .thumbnail-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                border-color: #1890ff;
                .thumbnail-overlay {
                    opacity: 1;
                }
            }

            img,
            video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .thumbnail-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 20px;
                opacity: 0;
                transition: opacity 0.3s;
            }

            &.video-thumbnail {
                .thumbnail-overlay {
                    opacity: 0.7;
                }
            }

            // 多文件指示器
            .media-count-badge {
                position: absolute;
                top: 4px;
                right: 4px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 10px;
                min-width: 20px;
                text-align: center;
                line-height: 1.2;
            }
        }
    }
}
</style>
