<template>
    <a-modal
        v-model:open="visible"
        width="800px"
        title="图片/视频"
        :footer="null"
        :bodyStyle="{ padding: '16px', overflow: 'auto', height: '600px' }"
        @cancel="handleClose"
    >
        <div class="media-preview-container">
            <!-- 轮播图组件 -->
            <a-carousel
                ref="carouselRef"
                :dots="false"
                :autoplay="false"
                v-model:current="currentIndex"
                :arrows="mediaList.length > 1"
                :afterChange="handleAfterChange"
            >
                <!-- 自定义箭头 -->
                <template #prevArrow>
                    <div class="custom-arrow custom-arrow-prev">
                        <LeftOutlined />
                    </div>
                </template>
                <template #nextArrow>
                    <div class="custom-arrow custom-arrow-next">
                        <RightOutlined />
                    </div>
                </template>

                <div v-for="(media, index) in mediaList" :key="index" class="carousel-item">
                    <!-- 图片预览 -->
                    <div v-if="media.type === 'image'" class="image-preview">
                        <img :src="media.url" alt="预览图片" />
                    </div>
                    <!-- 视频预览 -->
                    <div v-if="media.type === 'video'" class="video-preview">
                        <VideoPlayer
                            :src="media.url"
                            :key="'video-' + index"
                            :fluid="true"
                            :fit-mode="'contain'"
                            :autoplay="false"
                            :muted="false"
                        />
                    </div>
                </div>
            </a-carousel>

            <!-- 导航按钮 -->
            <div v-if="false" class="carousel-nav">
                <a-button @click="prevMedia" :disabled="currentIndex === 0">
                    <template #icon><LeftOutlined /></template>
                    上一个
                </a-button>
                <span class="nav-info">{{ currentIndex + 1 }} / {{ mediaList.length }}</span>
                <a-button @click="nextMedia" :disabled="currentIndex === mediaList.length - 1">
                    下一个
                    <template #icon><RightOutlined /></template>
                </a-button>
            </div>

            <!-- 缩略图导航 -->
            <div class="thumbnail-nav">
                <div
                    v-for="(media, index) in mediaList"
                    :key="'thumb-' + index"
                    class="thumbnail-nav-item"
                    :class="{ active: currentIndex === index }"
                    @click="goToSlide(index)"
                >
                    <!-- 图片缩略图 -->
                    <img v-if="media.type === 'image'" :src="media.url" alt="缩略图" />
                    <!-- 视频缩略图 -->
                    <div v-else-if="media.type === 'video'" class="video-thumbnail">
                        <video :src="media.url" muted></video>
                        <div class="video-play-icon">
                            <PlayCircleOutlined />
                        </div>
                    </div>
                    <div v-if="props.isDelete" class="del_icon" @click="deleteMedia(media, $event)">
                        <CloseCircleOutlined style="color: red; font-size: 18px" />
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<script setup name="MediaPreviewModal">
/**
 * 通用媒体预览组件
 * 支持图片和视频的轮播预览
 */

import { LeftOutlined, RightOutlined, PlayCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import VideoPlayer from './VideoPlayer.vue'

const props = defineProps({
    // 控制Modal显示隐藏
    open: {
        type: Boolean,
        default: false,
    },
    // 图片路径数组或逗号分隔的字符串
    imgPaths: {
        type: [Array, String],
        default: () => [],
    },
    // 视频路径数组或逗号分隔的字符串
    videoPaths: {
        type: [Array, String],
        default: () => [],
    },
    // 是否可以显示删除按钮 帮助用户删除上传的图或者视频
    isDelete: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['update:open', 'delete-media'])

// 响应式数据
const visible = computed({
    get: () => props.open,
    set: value => emit('update:open', value),
})

const currentIndex = ref(0)
const carouselRef = ref(null)

// 处理路径数据，统一转换为数组
const getPathArray = paths => {
    if (!paths) return []
    if (Array.isArray(paths)) return paths.filter(path => path && path.trim())
    if (typeof paths === 'string') return paths.split(',').filter(path => path && path.trim())
    return []
}

// 计算媒体列表
const mediaList = computed(() => {
    const list = []

    // 添加图片
    const imageList = getPathArray(props.imgPaths)
    imageList.forEach(url => {
        list.push({ type: 'image', url: url.trim() })
    })

    // 添加视频
    const videoList = getPathArray(props.videoPaths)
    videoList.forEach(url => {
        list.push({ type: 'video', url: url.trim() })
    })

    return list
})

// 上一个媒体 (保留用于导航按钮)
const prevMedia = () => {
    carouselRef.value?.prev()
}

// 下一个媒体 (保留用于导航按钮)
const nextMedia = () => {
    carouselRef.value?.next()
}

// 跳转到指定幻灯片
const goToSlide = index => {
    if (index >= 0 && index < mediaList.value.length && index !== currentIndex.value) {
        currentIndex.value = index
        carouselRef.value?.goTo(index)
    }
}

// 轮播切换后的回调，同步缩略图激活状态
const handleAfterChange = current => {
    currentIndex.value = current
}

// 关闭Modal
const handleClose = () => {
    visible.value = false
    // 重置索引
    currentIndex.value = 0
}

// 监听open变化，重置状态
watch(
    () => props.open,
    newOpen => {
        if (newOpen) {
            currentIndex.value = 0
        }
    },
)

// 删除媒体文件
const deleteMedia = async (media, event) => {
    // 阻止事件冒泡，防止触发缩略图点击事件
    if (event) {
        event.stopPropagation()
    }

    try {
        // 确认删除
        const confirmed = await yConfirm('确认删除', '确定要删除这个文件吗？')
        if (!confirmed) return

        // 记录删除前的索引信息
        const currentMediaIndex = mediaList.value.findIndex(item => item.type === media.type && item.url === media.url)
        const totalMediaCount = mediaList.value.length

        // 发送删除事件给父组件，传递媒体信息
        emit('delete-media', {
            type: media.type,
            url: media.url,
        })

        // 使用 nextTick 确保父组件数据更新后再调整索引
        await nextTick()

        if (currentMediaIndex !== -1) {
            // 如果删除后没有媒体了，关闭预览
            if (totalMediaCount <= 1) {
                handleClose()
                return
            }

            // 如果删除的是当前显示的媒体
            if (currentIndex.value === currentMediaIndex) {
                // 如果删除的是最后一个，索引向前移动
                if (currentIndex.value >= mediaList.value.length) {
                    currentIndex.value = Math.max(0, mediaList.value.length - 1)
                }
            } else if (currentIndex.value > currentMediaIndex) {
                // 如果删除的媒体在当前显示媒体之前，索引需要减1
                currentIndex.value = Math.max(0, currentIndex.value - 1)
            }

            // 确保索引在有效范围内
            if (currentIndex.value >= mediaList.value.length) {
                currentIndex.value = Math.max(0, mediaList.value.length - 1)
            }
        }

        YMessage.success('删除成功')
    } catch (error) {
        console.error('删除失败:', error)
        YMessage.error('删除失败')
    }
}
// 暴露方法给父组件
defineExpose({
    prevMedia,
    nextMedia,
    currentIndex,
})
</script>

<style lang="less" scoped>
.media-preview-container {
    .carousel-item {
        text-align: center;
        display: flex !important;
        align-items: center;
        justify-content: center;
        height: 500px;

        .image-preview {
            img {
                max-width: 100%;
                max-height: 500px;
                object-fit: contain;
                border-radius: 6px;
            }
        }

        .video-preview {
            position: relative;
            width: 100%;
            height: 500px;
            border-radius: 6px;
            overflow: hidden;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .carousel-nav {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        margin-top: 16px;

        .nav-info {
            color: #666;
            font-size: 14px;
            min-width: 60px;
            text-align: center;
        }
    }

    // 自定义箭头样式
    .custom-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex !important;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: translateY(-50%) scale(1.1);
        }

        &.custom-arrow-prev {
            left: 20px;
        }

        &.custom-arrow-next {
            right: 20px;
        }
    }

    // 缩略图导航样式
    .thumbnail-nav {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 12px;
        margin-top: 20px;
        padding: 0 20px;
        flex-wrap: wrap;

        .thumbnail-nav-item {
            position: relative;
            width: 96px;
            height: 96px;
            border: 2px solid transparent;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;

            .del_icon {
                position: absolute;
                top: 0px;
                right: 0px;
            }

            &:hover {
                border-color: #00b781;
                transform: scale(1.05);
            }

            &.active {
                border: 4px solid #00b781;
                transform: scale(1.1);
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .video-thumbnail {
                position: relative;
                width: 100%;
                height: 100%;

                video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .video-play-icon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: white;
                    font-size: 24px;
                    background: rgba(0, 0, 0, 0.6);
                    border-radius: 50%;
                    width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s;
                }
            }
        }
    }
}

:deep(.custom-arrow:before) {
    display: none;
}

// 轮播图样式覆盖
:deep(.ant-carousel) {
    .ant-carousel-dots {
        bottom: -40px;

        li {
            button {
                background: #d9d9d9;
                opacity: 0.6;

                &:hover {
                    background: #00b781;
                    opacity: 0.8;
                }
            }

            &.ant-carousel-dot-active {
                button {
                    background: #00b781;
                    opacity: 1;
                }
            }
        }
    }
}
</style>
