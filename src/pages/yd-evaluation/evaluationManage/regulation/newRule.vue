<template>
    <YDrawer v-model:open="state.open" :title="comtitle">
        <div>
            <a-form :model="state" ref="formRef">
                <div class="step_two_box">
                    <a-form-item
                        label="评价规则名称：："
                        :labelCol="{ span: 24 }"
                        mb-10
                        name="name"
                        :rules="[{ required: true, message: '请输入评价规则名称', trigger: 'blur' }]"
                    >
                        <a-input
                            style="width: 500px"
                            v-model:value.trim="state.name"
                            :disabled="state.isEdit === 'info'"
                            placeholder="请输入"
                            show-count
                            :maxlength="30"
                        />
                    </a-form-item>

                    <a-form-item label="评价规则：：" :labelCol="{ span: 24 }" mb-10 labelAlign="left" name="dataSource">
                        <a-table :dataSource="state.dataSource" bordered :pagination="false">
                            <!-- 把table的表头放在这里处理下 -->
                            <a-table-column
                                title="一级指标"
                                :width="260"
                                data-index="firstIndicatorName"
                                key="firstIndicatorName"
                                :customCell="
                                    (record, rowIndex, column) => {
                                        let index = 1
                                        for (let i = rowIndex + 1; i < state.dataSource.length; i++) {
                                            const element = state.dataSource[i]
                                            if (element['firstIndicatorId'] == record['firstIndicatorId']) {
                                                index = index + 1
                                            }
                                        }
                                        if (rowIndex > 0) {
                                            record['firstIndicatorId'] == state.dataSource[rowIndex - 1]?.firstIndicatorId && (index = 0)
                                        }
                                        return { rowSpan: index }
                                    }
                                "
                            />
                            <a-table-column title="二级指标" data-index="secondIndicatorName" key="secondIndicatorName" :width="260" />
                            <a-table-column title="评分标准" data-index="content" key="content" :width="300" />
                            <a-table-column title="评分方式" data-index="evalScoreTypeList" key="evalScoreTypeList" width="150px" />
                            <a-table-column title="评分范围" data-index="score" key="score" :width="150" />
                            <a-table-column
                                v-if="!(state.isEdit === 'info')"
                                title="操作"
                                data-index="operate"
                                key="operate"
                                :width="200"
                            />

                            <template #headerCell="{ column }">
                                <template v-if="column.key === 'firstIndicatorName'">
                                    <div class="ScoringCriteria_title">
                                        <span>{{ column.title }}</span>
                                        <img
                                            @click="addOne"
                                            v-if="!(state.isEdit === 'info')"
                                            src="@/assets/images/evaluation/ScoringCriteriaAdd.png"
                                            class="ScoringCriteriaAdd"
                                        />
                                    </div>
                                </template>
                            </template>
                            <template #bodyCell="{ column, record, index }">
                                <template v-if="column.key === 'firstIndicatorName'">
                                    <div v-if="record.firstIndicatorEdit" class="ScoringCriteria_title">
                                        <a-textarea
                                            v-model:value="record.firstIndicatorName"
                                            autosize
                                            :maxlength="50"
                                            placeholder="请输入"
                                            style="width: 230px"
                                        />
                                        <div class="iconcenter">
                                            <img
                                                @click="closeOne(record, index)"
                                                src="@/assets/images/evaluation/x.png"
                                                class="ScoringCriteriaAdd"
                                            />
                                            <img
                                                @click="addIndicator(record)"
                                                src="@/assets/images/evaluation/dui.png"
                                                class="ScoringCriteriaAdd"
                                            />
                                        </div>
                                    </div>
                                    <div class="ScoringCriteria_title" v-else>
                                        <span>{{ record.firstIndicatorName }}</span>
                                        <div class="iconcenter" v-if="!(state.isEdit === 'info')">
                                            <img
                                                @click="editFir(record)"
                                                src="@/assets/images/evaluation/ScoringCriteriaedit.png"
                                                class="ScoringCriteriaAdd editFir"
                                            />
                                            <!-- 当firstIndicatorId为0的时候 说明数据根本就没有保存下来不给加二级指标 -->
                                            <!-- 发现数组中有正在编辑的二级指标就不让他再加二级指标了 -->
                                            <img
                                                @click="addTwo(record)"
                                                v-if="record.firstIndicatorId && !isEditing"
                                                src="@/assets/images/evaluation/ScoringCriteriaAdd.png"
                                                class="ScoringCriteriaAdd"
                                            />
                                        </div>
                                    </div>
                                </template>
                                <template v-if="column.key === 'secondIndicatorName'">
                                    <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title">
                                        <a-textarea
                                            v-model:value="record.secondIndicatorName"
                                            autosize
                                            :maxlength="200"
                                            placeholder="请输入"
                                            style="width: 230px"
                                        />
                                        <div class="iconcenter"></div>
                                    </div>
                                    <div class="ScoringCriteria_title" v-else>
                                        <span>{{ record.secondIndicatorName }}</span>
                                    </div>
                                </template>
                                <template v-if="column.key === 'content'">
                                    <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title">
                                        <a-textarea
                                            v-model:value="record.content"
                                            style="width: 230px"
                                            autosize
                                            :maxlength="1000"
                                            placeholder="请输入"
                                        />
                                    </div>
                                    <div class="ScoringCriteria_title" v-else>
                                        <span>{{ record.content }}</span>
                                    </div>
                                </template>
                                <template v-if="column.key === 'score'">
                                    <div v-if="record.secondIndicatorEdit" class="ScoringCriteria_title items-center">
                                        <a-input-number
                                            placeholder="≥-100"
                                            v-model:value="record.minScore"
                                            :max="100"
                                            :min="-100"
                                            :precision="0"
                                            style="width: 60px"
                                        />
                                        <span pr-5 pl-5>-</span>
                                        <a-input-number
                                            placeholder="≤100"
                                            v-model:value="record.maxScore"
                                            :max="100"
                                            :min="-100"
                                            :precision="0"
                                            style="width: 60px"
                                        />
                                    </div>
                                    <div class="flex justify-center" v-else>
                                        <span>{{ record.minScore }}</span>
                                        <span>-</span>
                                        <span>{{ record.maxScore }}</span>
                                    </div>
                                </template>
                                <!-- 评分方式 -->
                                <template v-if="column.key === 'evalScoreTypeList'">
                                    <div v-if="record.secondIndicatorEdit">
                                        <a-select
                                            mode="multiple"
                                            v-model:value="record.evalScoreTypeList"
                                            :options="evalScoreTypeOptions"
                                            placeholder="请选择"
                                        ></a-select>
                                    </div>
                                    <div v-else>
                                        {{ arrayToDescription(record.evalScoreTypeList || []) }}
                                    </div>
                                </template>

                                <template v-else-if="column.key === 'operate'">
                                    <div v-if="record.secondIndicatorEdit">
                                        <a-button type="link" style="color: #00b781" @click="addIndicators(record)">保存</a-button>
                                        <a-button type="link" danger @click="delTwo(record, index, item)" style="padding: 0">取消</a-button>
                                    </div>
                                    <div v-else>
                                        <a-button
                                            type="link"
                                            style="color: #00b781"
                                            @click="editTwo(record)"
                                            v-if="record.firstIndicatorId != 0"
                                        >
                                            编辑
                                        </a-button>
                                        <a-button
                                            v-if="record.firstIndicatorId != 0"
                                            type="link"
                                            danger
                                            @click="delOperate(record, index, item)"
                                            style="padding: 0"
                                        >
                                            删除
                                        </a-button>
                                    </div>
                                </template>
                            </template>
                        </a-table>
                    </a-form-item>
                </div>
            </a-form>
        </div>
        <template #footer>
            <div v-if="!(state.isEdit === 'info')">
                <a-button mr-5 @click="close">取消</a-button>
                <a-button mr-5 @click="saveData" type="primary">保存</a-button>
            </div>
        </template>
    </YDrawer>
</template>
<script setup>
import { dataSourceDispose } from '../activity/configData'
const state = reactive({
    id: '',
    open: false,
    isEdit: false,
    name: '',
    dataSource: [],
    rulesData: [{ name: '', ruleName: '', personType: 0, evaluator: '', dataSource: [] }], // 前端收集的数据
})
const emit = defineEmits(['close'])
const formRef = ref(null)

function arrayToDescription(arr) {
    const mapping = {
        image: '图片',
        video: '视频',
        text: '文字',
        score: '分数',
    }

    // 通过map替换数组中的每个元素为对应描述
    const descriptions = arr.map(item => mapping[item] || item)
    // 将描述数组合成字符串，用空格分隔
    return descriptions.join(',')
}

const evalScoreTypeOptions = ref([
    { value: 'image', label: '图片' },
    { value: 'video', label: '视频' },
    { value: 'text', label: '文字' },
    { value: 'score', label: '分数' },

])

const comtitle = computed(() => {
    switch (state.isEdit) {
        case 'edit':
            return '编辑规则'
        case 'info':
            return '规则详情'
        case 'add':
            return '新增规则'
        default:
            return '规则' // 可以根据实际情况返回默认标题
    }
})

// 生成一个随机id , 生成出同样的那就当他是倒霉蛋算了
function generateRandomId(length) {
    // 初始化一个数组，包含0到9的数字
    const digits = Array.from({ length: 10 }, (_, i) => i)
    // 生成指定长度的随机数字串
    return Array.from({ length }, () => digits[Math.floor(Math.random() * digits.length)]).join('')
}

const props = defineProps({
    data: {
        type: Object,
        default: {},
    },
    validateInfos: {
        type: Object,
        default: {},
    },
})

const firName = reactive({})
const secondIndicatorList = reactive({})

// 这一步只需要清除他自己本来的数据
// 则一块只有两个逻辑 firstIndicatorId 是 0  他点取消了 就直接给他删了
// 不是0 就肯定是修改的数据取消 那就别动数据啊之前是啥 给他恢复一下啊
const closeOne = (row, index) => {
    if (row.firstIndicatorId === 0) {
        state.dataSource.splice(index, 1)
    } else {
        row.firstIndicatorName = firName[row.firstIndicatorId]
        row.firstIndicatorEdit = false
    }
}

const addIndicator = (item, origin) => {
    if (!item.firstIndicatorName) {
        YMessage.warning('一级指标名称不可为空!')
        return false
    }
    // 点击对号 有两种情况 要么是从刚创建的到填写, 要么是点之前的数据修改, 刚创建的就把id变成随机数呗 ,修改的话就不要动之前的数据了
    // 修改并且还得去找同样的id 要不然就改乱了
    if (item.firstIndicatorId === 0) {
        item.firstIndicatorId = generateRandomId(8)
    } else {
        // 找到firstIndicatorId 同样的数据一起改了去
        state.dataSource.forEach(
            obj => obj.firstIndicatorId === item.firstIndicatorId && (obj.firstIndicatorName = item.firstIndicatorName),
        )
    }
    // 改完那就关闭编辑状态了
    item.firstIndicatorEdit = false
}

// 保存按钮
const addIndicators = item => {
    if (!item.secondIndicatorName) {
        YMessage.warning('二级指标名称不可为空!')
        return false
    }
    // 这里还是有一样的 发现secondIndicatorId是 -1的话 就给他生成随机岁保存下来
    if (item.secondIndicatorId === -1) {
        item.secondIndicatorId = generateRandomId(8)
    }
    // 如果原来就有数据只是修改了的话,那就直接关闭就行了 反正到二级了已经是一条条数据了改就行了
    item.secondIndicatorEdit = false
}

const editTwo = item => {
    if (item.secondIndicatorId > 1) {
        // copy数据
        secondIndicatorList[item.secondIndicatorId] = JSON.parse(JSON.stringify(item))
    }
    item.secondIndicatorEdit = true
}

// 这里点击取消等于是要恢复原来的数据啊啊啊
const delTwo = (row, index, item) => {
    if (row.secondIndicatorId !== -1) {
        // 如果是secondIndicatorId 已经是随机数了 等于是修改 然后点取消这里得恢复数据
        // 恢复数据 想恢复什么就恢复什么
        row.secondIndicatorName = secondIndicatorList[row.secondIndicatorId].secondIndicatorName
        row.content = secondIndicatorList[row.secondIndicatorId].content
        row.minScore = secondIndicatorList[row.secondIndicatorId].minScore
        row.maxScore = secondIndicatorList[row.secondIndicatorId].maxScore
    } else {
        // 去找一级指标 如果一级指标对应到多个二级指标的时候就直接删他
        // 如果是 一级指标对应到只有一个二级指标 那就只能做清空的操作 要不然把整个数据都删了
        let list = state.dataSource.filter(i => i.firstIndicatorId == row.firstIndicatorId)

        // 清数据
        row.secondIndicatorName = ''
        row.content = ''
        row.minScore = ''
        row.maxScore = ''

        if (list.length > 1) {
            state.dataSource.splice(index, 1)
        }
    }
    row.secondIndicatorEdit = false
}

// 这个是从整个table数据源做删除的逻辑
const delOperate = async (row, index, item) => {
    const flag = await yConfirm('删除提示', '确定删除此数据?')
    // 找到下标直接删就完事了啊
    if (flag) {
        state.dataSource.splice(index, 1)
    }
}

const editFir = item => {
    if (item.firstIndicatorId != 0) {
        firName[item.firstIndicatorId] = item.firstIndicatorName
    }
    item.firstIndicatorEdit = true
}

// 点击新增二级指标的加号
// 需要隐藏一下 二级指标加号的按
const addTwo = item => {
    // 这条数据中的 secondIndicatorId 处于 -1 的编辑状态的话 就不给他加一条数据
    // 直接把状态打开让他写
    if (item.secondIndicatorId === -1) {
        item.secondIndicatorEdit = true
        return false
    }

    // 在数据源origin的列表中去找firstIndicatorId 找到一样的就直接在数组中插入一条数据啊
    const index = state.dataSource.findLastIndex(i => i.firstIndicatorId == item.firstIndicatorId)
    state.dataSource.splice(index + 1, 0, {
        firstIndicatorId: item.firstIndicatorId,
        firstIndicatorName: item.firstIndicatorName,
        secondIndicatorId: -1,
        secondIndicatorEdit: true,
        secondIndicatorName: '',
        content: '',
        minScore: '',
        maxScore: '',
    })
}

// 添加一级指标如果有一级指标的输入框存在激活状态的就不让他点
const addOne = () => {
    const flag = state.dataSource.findIndex(i => i.firstIndicatorId == 0)

    if (flag == -1) {
        // 如果是新添加进来的指标就补齐所有字段啊啊啊啊
        state.dataSource.push({
            firstIndicatorId: 0,
            firstIndicatorName: '',
            secondIndicatorId: -1,
            secondIndicatorName: '',
            firstIndicatorEdit: true,
            content: '',
            minScore: '',
            maxScore: '',
        })
    }
}

// 如果过滤整个数组 发现数组中有正在编辑的二级指标就不让他再加二级指标了
const isEditing = computed(() => {
    const arr = state.dataSource.filter(i => i.secondIndicatorId == -1 && i.secondIndicatorEdit)
    return arr.length > 0
})

const showModel = (isEdit, data) => {
    state.isEdit = isEdit
    if (state.isEdit === 'edit' || state.isEdit === 'info') {
        state.id = data.id
        http.get('/cloud/evalPublicRule/get', { id: data.id }).then(res => {
            const tableData = res.data.firstIndicators.flatMap(item => {
                let data = []
                item.secondIndicators.forEach(secondIndicator => {
                    data.push({
                        firstIndicatorId: item.id,
                        firstIndicatorName: item.name,
                        secondIndicatorId: secondIndicator.id,
                        secondIndicatorName: secondIndicator.name,
                        indicatorScoreId: secondIndicator.indicatorScore.id,
                        content: secondIndicator.indicatorScore.content,
                        minScore: secondIndicator.indicatorScore.minScore,
                        maxScore: secondIndicator.indicatorScore.maxScore,
                        evalScoreTypeList: secondIndicator.indicatorScore.evalScoreTypeList,
                    })
                })
                return data
            })
            state.dataSource = tableData
            state.name = res.data.name
            state.open = true
        })
    } else {
        state.dataSource = []
        state.name = ''
        state.open = true
    }
}

const close = () => {
    state.dataSource = []
    state.name = ''
    state.open = false
    emit('close')
}
const saveData = () => {
    formRef.value.validate().then(() => {
        if (state.dataSource.length === 0) {
            YMessage.warning('请添加评价规则!')
            return
        }

        const requiredFields = ['firstIndicatorName', 'secondIndicatorName', 'content', 'evalScoreTypeList'] // 需要校验的字段名

        if (state.dataSource.some(i => requiredFields.some(field => !i.hasOwnProperty(field) || !i[field]))) {
            YMessage.warning('请补全评价规则!')
            return
        }

        if (state.isEdit === 'edit') {
            const params = {
                id: state.id,
                name: state.name,
                firstIndicators: dataSourceDispose(state.dataSource),
            }
            http.post('/cloud/evalPublicRule/update', params).then(res => {
                YMessage.success('修改成功')
                close()
            })
        } else {
            const disarr = dataSourceDispose(state.dataSource)
            const params = {
                name: state.name,
                firstIndicators: disarr,
            }
            http.post('/cloud/evalPublicRule/create', params)
                .then(res => {
                    YMessage.success('操作成功!')
                    close()
                })
                .catch(e => {
                    console.log('e', e)
                })
        }
    })
}
defineExpose({
    showModel,
})
</script>
<style scoped lang="less">
.step_two_box {
    // position: relative;

    .ScoringCriteriaAdd {
        width: 20px;
        height: 20px;
        cursor: pointer;

        &.editFir {
            display: none;
        }
    }

    .iconcenter {
        display: flex;
        align-items: center;
    }

    .ScoringCriteriaAdd + .ScoringCriteriaAdd {
        margin-left: 8px;
    }
    .ScoringCriteria_title {
        display: flex;
        justify-content: space-between;

        &:hover .ScoringCriteriaAdd {
            display: block;
        }
    }
}
</style>
