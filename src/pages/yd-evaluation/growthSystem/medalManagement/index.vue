<template>
    <cont-header />
    <div p-20>
        <div pb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="勋章名称：">
                            <a-input :allowClear="true" v-model:value.trim="query.medalName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="勋章分类：">
                            <a-select
                                :options="state.evalTypeListArr"
                                :allowClear="true"
                                :field-names="{
                                    label: 'name',
                                    value: 'id',
                                }"
                                v-model:value="query.evalTypeId"
                                placeholder="请输入"
                            ></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="勋章状态：">
                            <a-select :allowClear="true" v-model:value="query.medalStatus" placeholder="请输入">
                                <a-select-option :value="null">全部</a-select-option>
                                <a-select-option :value="1">启用</a-select-option>
                                <a-select-option :value="0">停用</a-select-option>
                            </a-select>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="发放形式：">
                            <a-select :allowClear="true" v-model:value="query.issuanceMethod" placeholder="请输入">
                                <a-select-option :value="null">全部</a-select-option>
                                <a-select-option :value="1">自动获得</a-select-option>
                                <a-select-option :value="2">手动发放</a-select-option>
                            </a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="更新时间：">
                            <RangePicker v-model:startTime="query.updateStartTime" v-model:endTime="query.updateEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="queryData">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="reset">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div flex justify-end mb-24 mt-20>
            <a-button type="primary" @click="grantMedal">发放勋章</a-button>
            <a-button @click="openMedal">新建勋章</a-button>
            <a-button :disabled="!selected.length" @click="handleOperation(1)">批量启用</a-button>
            <a-button :disabled="!selected.length" @click="handleOperation(0)">批量禁用</a-button>
        </div>
        <div min-h-520 mb-20>
            <ETable
                hash="readersList"
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                :row-selection="{
                    selectedRowKeys: selected,
                    onChange: onSelectChange,
                }"
                @change="handleTableChange"
            >
                <a-table-column title="勋章编号" data-index="medalCode" :width="100" />
                <a-table-column title="勋章图标" data-index="medalIconUrl" :width="100">
                    <template #default="{ record, text }">
                        <div class="medalIconUrlImg">
                            <img :src="record.medalIconUrl" />
                        </div>
                    </template>
                </a-table-column>
                <a-table-column title="评价分类" data-index="evalTypeName" :width="100" />
                <a-table-column title="勋章名称" data-index="medalName" :width="100" />
                <a-table-column title="发放形式" data-index="issuanceMethod" :width="100">
                    <template #default="{ record, text }">
                        {{ getStatusObj(record.issuanceMethod) }}
                    </template>
                </a-table-column>
                <a-table-column title="勋章释义" data-index="medalDescription" :width="200" />
                <a-table-column title="勋章状态" data-index="medalStatus" :width="100">
                    <template #default="{ record, text }">
                        <a-badge :color="getmedalStatusObj(record.medalStatus).color" :text="getmedalStatusObj(record.medalStatus).text" />
                    </template>
                </a-table-column>
                <a-table-column title="积分要求" data-index="satisfyScore" :width="100">
                    <template #default="{ record, text }">
                        {{ text ? text + '积分' : '-' }}
                    </template>
                </a-table-column>
                <a-table-column title="勋章奖励" data-index="medalScore" :width="100">
                    <template #default="{ record, text }">
                        {{ text ? text + '积分' : '-' }}
                    </template>
                </a-table-column>

                <a-table-column title="更新时间" data-index="updateTime" :width="100" :sorter="true" />
                <a-table-column title="操作" data-index="operate" :width="200">
                    <template #default="{ record }">
                        <a-button type="link" class="btn-link-color" @click="syncHumanFace(record)">编辑</a-button>
                        <a-button type="link" class="btn-link-color" @click="particulars(record)">详情</a-button>
                        <a-button v-if="record.medalStatus === 0" type="link" class="btn-link-color" @click="changeMedalStatus(record, 1)">
                            启用
                        </a-button>
                        <a-button v-if="record.medalStatus === 1" type="link" class="btn-link-color" @click="changeMedalStatus(record, 0)">
                            停用
                        </a-button>
                    </template>
                </a-table-column>
            </ETable>
        </div>
    </div>

    <!-- 新增勋章记录的抽屉 -->
    <a-drawer :width="500" @close="closeThing" v-model:open="state.medalOpen" :keyboard="false" :destroyOnClose="true" :title="title">
        <div class="addedOrder">
            <a-form
                layout="vertical"
                :model="addedFormState"
                ref="addedFormStateRef"
                autocomplete="off"
                :label-col="{ span: 8 }"
                :wrapper-col="{ span: 16 }"
            >
                <a-form-item
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    :rules="[{ required: true, message: '请上传勋章图标!', trigger: ['change', 'blur'] }]"
                    name="medalIconUrl"
                    label="勋章图标："
                >
                    <div flex flex-items-center>
                        <div w-100 h-100>
                            <uploadImage v-model:url="addedFormState.medalIconUrl" :limit="2" />
                        </div>
                    </div>
                </a-form-item>

                <div class="iconStoreRoom" @click="selectIconStoreRoom">勋章图标库</div>

                <a-form-item
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    name="evalTypeId"
                    :rules="[{ required: true, message: '请选择!' }]"
                    label="评价分类："
                    mb-6
                >
                    <a-select
                        placeholder="请选择"
                        v-model:value="addedFormState.evalTypeId"
                        :options="state.evalTypeList"
                        :field-names="{
                            label: 'name',
                            value: 'id',
                        }"
                    ></a-select>
                </a-form-item>
                <a-form-item
                    label="勋章名称："
                    name="medalName"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    mt-12
                    :rules="[{ required: true, message: '请输入勋章名称!' }]"
                >
                    <a-input v-model:value.trim="addedFormState.medalName" show-count :maxlength="30" placeholder="请输入" />
                </a-form-item>
                <a-form-item
                    label="勋章释义："
                    name="medalDescription"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    mt-12
                    :rules="[{ required: true, message: '请输入勋章释义!' }]"
                >
                    <a-textarea
                        :auto-size="{ minRows: 2, maxRows: 5 }"
                        show-count
                        :maxlength="200"
                        v-model:value.trim="addedFormState.medalDescription"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item
                    label="发放形式："
                    name="issuanceMethod"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    mt-12
                    :rules="[
                        { required: true, message: '请选择获取方式' },
                        { validator: validaSatisfyScore, required: true, trigger: 'blur' },
                    ]"
                >
                    <a-radio-group v-model:value="addedFormState.issuanceMethod" name="radioGroup" @change="changeIssuanceMethod">
                        <a-radio flex align-center :value="1">
                            自动获得 累计达到
                            <a-input-number
                                style="width: 129px"
                                :max="10000"
                                :precision="2"
                                placeholder="请输入"
                                v-model:value.trim="addedFormState.satisfyScore"
                            ></a-input-number>
                            积分
                        </a-radio>
                        <a-radio flex pt-16 :value="2">手动发放</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="勋章奖励：" name="medalScore" :label-col="{ span: 4 }" mt-24>
                    <a-input-number
                        style="width: 129px"
                        :max="state.maxMedalScore"
                        :precision="2"
                        placeholder="请输入积分值"
                        v-model:value="addedFormState.medalScore"
                    ></a-input-number>
                    积分
                </a-form-item>
            </a-form>
        </div>
        <template #footer>
            <div class="footer">
                <a-button @click="closeThing">取消</a-button>
                <a-button type="primary" @click="submitThing">确认</a-button>
            </div>
        </template>
    </a-drawer>

    <!-- 详情的抽屉 单独写就完事了单独写就完事了单独写就完事了单独写就完事了单独写就完事了 -->
    <a-drawer
        :width="500"
        @close="closeThingDetails"
        v-model:open="state.medalOpenDetails"
        :keyboard="false"
        :destroyOnClose="true"
        title="勋章详情"
    >
        <div class="addedOrder">
            <div p-b-24>
                勋章编号：
                <span>{{ addedFormStateDetails.medalCode }}</span>
            </div>
            <div p-b-24>
                勋章状态：
                <a-badge
                    :color="getmedalStatusObj(addedFormStateDetails.medalStatus).color"
                    :text="getmedalStatusObj(addedFormStateDetails.medalStatus).text"
                />
            </div>
            <div p-b-16>勋章图标：</div>
            <div p-b-24>
                <div text-center w-130>
                    <a-image
                        :width="100"
                        :height="100"
                        :preview="{
                            visible,
                            onVisibleChange: setVisible,
                        }"
                        :src="addedFormStateDetails.medalIconUrl"
                    />
                    <div class="lookImg" @click="() => setVisible(true)">查看大图</div>
                </div>
            </div>
            <div p-b-8>评价分类：</div>
            <div p-b-24>
                <a-select
                    w-full
                    disabled
                    placeholder="请选择"
                    v-model:value="addedFormStateDetails.evalTypeId"
                    :options="state.evalTypeList"
                    :field-names="{
                        label: 'name',
                        value: 'id',
                    }"
                ></a-select>
            </div>
            <div p-b-8>勋章名称：</div>
            <div p-b-24>
                <a-input disabled v-model:value.trim="addedFormStateDetails.medalName" show-count :maxlength="30" placeholder="请输入" />
            </div>
            <div p-b-8>勋章释义：</div>
            <div p-b-24>
                <a-textarea
                    disabled
                    :auto-size="{ minRows: 2, maxRows: 5 }"
                    show-count
                    :maxlength="200"
                    v-model:value.trim="addedFormStateDetails.medalDescription"
                    placeholder="请输入"
                />
            </div>
            <div p-b-8>发放形式：</div>
            <div p-b-24>
                <a-radio-group
                    disabled
                    v-model:value="addedFormStateDetails.issuanceMethod"
                    name="radioGroup"
                    @change="changeIssuanceMethod"
                >
                    <a-radio flex align-center :value="1">
                        自动获得 累计达到
                        <a-input-number
                            disabled
                            style="width: 129px"
                            :max="10000"
                            :precision="2"
                            placeholder="请输入"
                            v-model:value.trim="addedFormStateDetails.satisfyScore"
                        ></a-input-number>
                        积分
                    </a-radio>
                    <a-radio flex pt-16 :value="2">手动发放</a-radio>
                </a-radio-group>
            </div>
            <div p-b-24>
                勋章奖励：
                <a-input-number
                    style="width: 129px"
                    disabled
                    :precision="2"
                    placeholder="请输入积分值"
                    v-model:value="addedFormStateDetails.medalScore"
                ></a-input-number>
                积分
            </div>

            <div p-b-24>
                更新时间：
                <span>{{ addedFormStateDetails.updateTime }}</span>
            </div>
            <div p-b-24>
                更新人：
                <span>{{ addedFormStateDetails.updateBy }}</span>
            </div>
        </div>
        <template #footer>
            <div class="footer">
                <a-button @click="closeThingdetails">取消</a-button>
                <a-button type="primary" @click="closeThingdetails">确认</a-button>
            </div>
        </template>
    </a-drawer>

    <!-- 这是发放勋章的抽屉 -->
    <a-drawer
        :width="500"
        v-model:open="state.grantMedalOpen"
        :keyboard="false"
        :destroyOnClose="true"
        title="发放勋章"
        @close="handleClose"
    >
        <div class="grantMedalDrawer">
            <a-form
                :model="grantMedalForm"
                layout="vertical"
                ref="grantMedalFormRef"
                name="grantMedalForm"
                autocomplete="off"
                :label-col="{ span: 8 }"
                :wrapper-col="{ span: 16 }"
            >
                <a-form-item
                    label="选择发放学生："
                    name="cardScore"
                    :rules="[{ required: true, message: '请选择发放学生' }]"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                >
                    <a-input v-model:value="grantMedalForm.cardScore" @click="handleSelectModel" placeholder="请选择" />
                </a-form-item>
            </a-form>
            <div v-if="state.pages.medalList.length" mt-20>
                <div mb-16>选择勋章：</div>
                <div>
                    <a-row :gutter="[25, 16]">
                        <a-col :span="8" v-for="(item, index) in state.pages.medalList" :key="item.value" text-center>
                            <div class="medalbox_ch" @click="choiceMedal(item)">
                                <div class="medalbox_radio_ch">
                                    <img v-if="item.isChoice" src="@/assets/images/admin/medal_yes.png" />
                                    <img v-else src="@/assets/images/admin/medal_no.png" />
                                </div>
                                <div class="medalbox_img_ch">
                                    <img :src="item.label" alt="" />
                                </div>
                            </div>
                            <span class="text_name" :title="item.name">{{ item.name }}</span>
                        </a-col>
                    </a-row>
                </div>
            </div>
            <div v-else mt-40 text-center w-full class="color-#5c5c5c">暂无可发放勋章</div>

            <div>
                <a-button v-if="state.pages.pageNo * state.pages.pageSize < state.pages.total" @click="getMedal(true)">加载更多</a-button>
            </div>
        </div>
        <template #footer>
            <a-button @click="handleClose">取消</a-button>
            <a-button type="primary" @click="handleMedals" :disabled="!state.pages.medalList.length">确定</a-button>
        </template>
    </a-drawer>

    <!-- 整一个勋章图标库的弹窗modal -->
    <a-modal
        v-model:open="state.iconLibraryOpen"
        width="500px"
        title="勋章图标库"
        :bodyStyle="{ padding: '16px', overflow: 'auto', maxHeight: '550px' }"
        @ok="handleConfirm"
    >
        <div class="iconLibraryBox">
            <a-row :gutter="[25, 16]">
                <a-col :span="8" v-for="(item, index) in state.medalList" :key="item.id">
                    <div class="medalbox" flex justify-center @click="selectiveMedal(item)">
                        <div class="medalbox_radio">
                            <img v-if="checkImg === item.id" src="@/assets/images/admin/medal_yes.png" />
                            <img v-else src="@/assets/images/admin/medal_no.png" />
                        </div>
                        <div class="medalbox_img">
                            <img :src="item.img" alt="" />
                            <span class="text" :title="item.name">{{ item.name }}</span>
                        </div>
                    </div>
                </a-col>
            </a-row>
        </div>
    </a-modal>

    <!-- 选人组件 -->
    <ModelSelect v-model:openVisible="modelState.openVisible" :tabs="state.peopleTabs" :selected="state.selectEcho" />
</template>
<script setup name="medalManagement">
import uploadImage from './uploadImage.vue'

const issuanceMethodObj = {
    1: '自动发放',
    2: '手动发放',
    default: '-',
}

function getStatusObj(key) {
    return issuanceMethodObj[key] || issuanceMethodObj.default
}

const visible = ref(false)
const setVisible = value => {
    visible.value = value
}

const addedFormState = ref({
    medalIconUrl: '',
})

const addedFormStateDetails = ref({
    medalIconUrl: '',
})

const addedFormStateRef = ref()
const grantMedalForm = reactive({
    cardScore: '',
    medalCodeList: [],
})

const selected = ref([])

const state = reactive({
    medalOpen: false,
    isEdit: false,
    grantMedalOpen: false,
    img: '',
    maxMedalScore: null,
    medalDetails: false,
    iconLibraryOpen: false,
    evalTypeList: [],
    evalTypeListArr: [],
    peopleTabs: [
        {
            tab: '学生',
            checked: true,
            checkVisible: 'all',
            id: 0,
            personField: { key: 'typeValue', value: ['student'] },
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ],
    selectEcho: [],
    pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        medalList: [],
    },
    medalList: [
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal14.png',
            name: '安全保护意识强',
            id: 0,
        },
        { img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal15.png', name: '按时完成作业', id: 1 },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal16.png',
            name: '诚实守信',
            id: 2,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal17.png',
            name: '环保卫士',
            id: 3,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal18.png',
            name: '好好学习lv1',
            id: 4,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal19.png',
            name: '热爱学习',
            id: 5,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal20.png',
            name: '劳动小将士lv1',
            id: 6,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal1.png',
            name: '热爱劳动',
            id: 7,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal2.png',
            name: '热爱运动',
            id: 8,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal3.png',
            name: '热爱艺术',
            id: 9,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal4.png',
            name: '书写整洁',
            id: 10,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal5.png',
            name: '完成1次评价',
            id: 11,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal6.png',
            name: '文明礼仪星Lv1',
            id: 12,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal7.png',
            name: '想象力丰富',
            id: 13,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal8.png',
            name: '运动健将lv1',
            id: 14,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal9.png',
            name: '艺术之星lv1',
            id: 15,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal10.png',
            name: '友善和睦',
            id: 16,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal11.png',
            name: '勇于挑战',
            id: 17,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal12.png',
            name: '遵纪守时',
            id: 18,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/medal13.png',
            name: '助人为乐',
            id: 19,
        },
    ],
})

const statusObj = {
    1: {
        text: '启用',
        color: '#00B781',
    },
    0: {
        text: '停用',
        color: '#595959',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

const orderType = {
    ascend: '1',
    descend: '2',
}

// 关联弹窗
const modelState = reactive({
    openVisible: false, // 显示弹框
    dataSource: [], // 左侧数据源
    checkVisible: 'all', // 能选什么就选什么啊
    spinning: false, // loading
    disableSelect: [], // 禁止选择的ids
    searchTable: [], // 选人搜索 table 中显示
    globalID: '', // 最顶成id
})
provide('modelState', () => modelState)
provide('callbackFunction', () => ({
    search: searchSelect,
    toggleLevel,
    cancel: closeSelect,
    submit,
}))

// 获取到选人组件数据
function getSelectTree(options) {
    /**
     * 1. 教职工 treeType：2 ， businessType：21
     * 2. 学生  treeType：1 ， businessType：11
     * 3. 家长  treeType：1 ， businessType：12
     * 4. pid第一次为0.之后根据层级id请求
     */
    modelState.spinning = true
    const params = {
        treeType: 1,
        pid: 0,
        typeValue: null,
        businessType: 11,
        code: null,
        isRule: true,
        ...options,
    }

    http.post('/cloud/v3/tree/selectTree', params)
        .then(({ data }) => {
            setDataSource(data)
        })
        .finally(() => {
            modelState.spinning = false
        })
}
getSelectTree({ treeType: 1, businessType: 11 })

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || []
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const firstLevel = !options.index
    let params = {
        treeType: item.treeType,
        businessType: item.businessType,
        typeValue: item.typeValue,
        pid: item.id,
    }
    // 首层数据
    if (firstLevel) {
        params = {
            treeType: 1,
            businessType: 11,
        }
    }
    getSelectTree(params)
}

// 查找
function searchSelect(tabId, name) {
    modelState.spinning = true
    const options = {
        treeType: 1,
        businessType: 11,
        code: null,
        isRule: true,
        searchKey: name,
        pageNo: 1,
        // 目前没有分页，最大条数
        pageSize: 100,
    }
    http.post('/cloud/v3/tree/selectTree/search', options)
        .then(({ data }) => {
            setDataSource(data.list || [])
        })
        .finally(() => {
            modelState.spinning = false
        })
}

const closeSelect = () => {}

// 点击确定的人员 最重要的是要知道这块是确认给谁的数据啊
const submit = checked => {
    // console.log('确定好的人员checked', checked)
    state.selectEcho = checked
    grantMedalForm.cardScore = checked.map(i => i.name).join('、')
}

const handleSelectModel = () => {
    getSelectTree({ treeType: 1, businessType: 11 })
    modelState.openVisible = true
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]
    getList()
}

function getmedalStatusObj(key) {
    return statusObj[key] || statusObj.default
}

watch(
    () => addedFormState.value.medalIconUrl,
    val => {
        if (addedFormStateRef.value) {
            addedFormStateRef.value.validate('medalIconUrl')
        }
    },
    {
        deep: true,
    },
)

// 获取方式的自定义校验
const validaSatisfyScore = (rule, value) => {
    if (addedFormState.value.issuanceMethod === 1) {
        if (addedFormState.value.satisfyScore === '') {
            return Promise.reject(new Error('请输入积分值!'))
        }
        return Promise.resolve()
    } else {
        return Promise.resolve()
    }
}
const { query, page, getList, reset, paginationChange } = useList('/cloud/evalMedal/pageEvalMedal', {
    medalStatus: null,
    evalTypeId: null,
    issuanceMethod: null,
})
getList()

function queryData() {
    getList()
}

const title = computed(() => {
    return state.isEdit ? '编辑勋章' : '新增勋章'
})

// 打开新建勋章的抽屉
const openMedal = () => {
    state.medalOpen = true
    http.get('/cloud/evalMedal/maxMedalScore').then(res => {
        state.maxMedalScore = res.data
    })
}

// 编辑
const syncHumanFace = item => {
    state.isEdit = true
    Object.assign(addedFormState.value, item)
    openMedal()
}

// 查看勋章详情
const particulars = item => {
    Object.assign(addedFormStateDetails.value, item)
    state.medalOpenDetails = true
}
const closeThingdetails = () => {
    state.medalOpenDetails = false
}

const checkImg = ref('')
// 发放勋章的时候 选择勋章
const selectiveMedal = data => {
    checkImg.value = data.id
    state.img = data.img
}

// 多选勋章
const choiceMedal = data => {
    data.isChoice = !data.isChoice
}

// 勋章图标库确定
const handleConfirm = () => {
    addedFormState.value.medalIconUrl = state.img
    state.iconLibraryOpen = false
}

const grantMedalFormRef = ref(null)
// 发放勋章
const handleMedals = () => {
    if (!state.pages.medalList.length) return YMessage.warning('暂无可发放勋章')
    grantMedalFormRef.value.validate().then(res => {
        if (!state.pages.medalList.some(item => item.isChoice === true)) return YMessage.warning('请选择勋章')
        const params = {
            personListDTO: state.selectEcho.map(i => ({
                id: i.id,
                name: i.name,
                typeValue: i.typeValue,
                identity: i._type,
            })),
            medalCodeList: state.pages.medalList.filter(item => item.isChoice === true).map(item => item.value),
        }
        http.post('/cloud/evalMedal/person/issuanceMedal', params).then(res => {
            YMessage.success('发放成功')
            handleClose()
            reset()
        })
    })
}

// 关闭弹窗
const handleClose = () => {
    state.grantMedalOpen = false
    Object.keys(grantMedalForm).forEach(i => (grantMedalForm[i] = null))
    state.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        medalList: [],
    }
}

const getEvalTypeList = () => {
    http.get('/cloud/evalType/listBySchool').then(res => {
        state.evalTypeList = res.data || []
        state.evalTypeListArr = [{ name: '全部', id: null }, ...res.data] || []
    })
}
getEvalTypeList()

// 批量选择
const onSelectChange = (selectedRowKeys, selectedRow) => {
    selected.value = selectedRowKeys
}

// 批量启用/禁用
const handleOperation = isEnable => {
    const params = {
        ids: selected.value,
        medalStatus: isEnable,
    }
    http.post('/cloud/evalMedal/batchUpdateStatus', params).then(res => {
        YMessage.success(`批量${isEnable ? '启用' : '禁用'}成功`)
        selected.value = []
        reset()
    })
}

// 改这个勋章状态
const changeMedalStatus = (data, status) => {
    http.post('/cloud/evalMedal/batchUpdateStatus', {
        ids: [data.id],
        medalStatus: status,
    }).then(res => {
        YMessage.success('修改成功!')
        getList()
    })
}

// 也可以选择图标库的url
const selectIconStoreRoom = () => {
    state.iconLibraryOpen = true
}

// 确认新建/编辑
const submitThing = () => {
    // 表单校验
    addedFormStateRef.value.validate().then(res => {
        if (state.isEdit) {
            http.post('/cloud/evalMedal/updateEvalMedal', addedFormState.value)
                .then(res => {
                    YMessage.success('更新成功!')
                    closeThing()
                })
                .catch(e => {})
        } else {
            http.post('/cloud/evalMedal/createEvalMedal', addedFormState.value)
                .then(res => {
                    YMessage.success('新建成功!')
                    closeThing()
                })
                .catch(e => {})
        }
    })
}

const closeThing = () => {
    state.medalOpen = false
    state.isEdit = false
    addedFormState.value = {
        medalIconUrl: '',
    }
    reset()
}

// 改变方式
const changeIssuanceMethod = e => {
    addedFormState.value.satisfyScore = ''
}

// 获取勋章
const getMedal = data => {
    if (data) {
        state.pages.pageNo++
    }
    const params = {
        medalStatus: 1,
        issuanceMethod: 2,
        ...state.pages,
    }
    http.post('/cloud/evalMedal/pageEvalMedal', params).then(res => {
        state.pages = { ...state.pages, ...res.data }
        const list = res.data.list.map(i => ({ label: i.medalIconUrl, value: i.medalCode, name: i.medalName }))
        state.pages.medalList.push(...list)
    })
}

// 打开发放勋章的弹窗
const grantMedal = () => {
    state.grantMedalOpen = true
    state.selectEcho = []
    getMedal()
}
</script>
<style lang="less" scoped>
.medalbox {
    cursor: pointer;
    position: relative;
    .medalbox_img {
        width: 110px;
        // height: 110px;
        background: #ffffff;
        // border: 1px solid #f0f2f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // border-radius: 50%;
        margin-bottom: 20px;
        img {
            width: 110px;
            height: 110px;
        }
        .text {
            width: 110px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
        }
    }
    .medalbox_radio {
        right: 10px;
        width: 18px;
        height: 18px;
        position: absolute;
        img {
            width: 18px;
            height: 18px;
        }
    }
}

.medalbox_ch {
    cursor: pointer;
    position: relative;
    .medalbox_img_ch {
        // width: 110px;
        height: 110px;
        background: #ffffff;
        border: 1px solid #f0f2f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
            width: 110px;
            height: 110px;
        }
    }
    .medalbox_radio_ch {
        right: 0;
        width: 18px;
        height: 18px;
        position: absolute;
        img {
            width: 18px;
            height: 18px;
        }
    }
}

.text_name {
    display: inline-block;
    width: 110px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

.medalIconUrlImg {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid #f0f2f5;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
        width: 70%;
        height: 70%;
    }
}

.iconStoreRoom {
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
    cursor: pointer;
    padding-top: 12px;
    padding-bottom: 12px;
}

.lookImg {
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
    cursor: pointer;
}
</style>
